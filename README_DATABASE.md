# TianyiSport 数据库配置说明

## 概述
本项目使用MySQL数据库，数据库名称为 `TianyiSport`，用于管理运动相关的数据。

## 数据库配置

### 默认配置
- **数据库服务器**: localhost
- **端口**: 3306
- **数据库名**: TianyiSport
- **用户名**: root
- **密码**: 空（请根据实际情况修改）

### 修改数据库配置
如需修改数据库连接配置，请编辑以下文件：
```
src/main/java/com/tianyi/util/DatabaseUtil.java
```

修改以下常量：
```java
private static final String DB_HOST = "localhost";        // 数据库主机
private static final String DB_PORT = "3306";            // 数据库端口
private static final String DB_USERNAME = "root";        // 用户名
private static final String DB_PASSWORD = "";            // 密码
```

## 使用方法

### 方法1：通过Java程序创建（推荐）
1. 确保MySQL服务已启动
2. 运行Main.java程序
3. 程序会自动创建数据库和表结构

### 方法2：通过SQL脚本创建
1. 启动MySQL命令行或使用MySQL Workbench
2. 执行 `database/create_tianyi_sport.sql` 脚本

```bash
mysql -u root -p < database/create_tianyi_sport.sql
```

## 数据库结构

### 1. users（用户表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| username | VARCHAR(50) | 用户名，唯一 |
| email | VARCHAR(100) | 邮箱，唯一 |
| password | VARCHAR(255) | 密码 |
| full_name | VARCHAR(100) | 真实姓名 |
| phone | VARCHAR(20) | 手机号码 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 2. sports（运动项目表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| name | VARCHAR(100) | 运动项目名称 |
| description | TEXT | 运动项目描述 |
| category | VARCHAR(50) | 运动类别 |
| created_at | TIMESTAMP | 创建时间 |

### 3. sport_records（运动记录表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| user_id | INT | 用户ID，外键 |
| sport_id | INT | 运动项目ID，外键 |
| duration_minutes | INT | 运动时长（分钟） |
| calories_burned | INT | 消耗卡路里 |
| notes | TEXT | 备注 |
| record_date | DATE | 运动日期 |
| created_at | TIMESTAMP | 创建时间 |

## 预置数据
系统会自动插入以下运动项目：
- 跑步（有氧运动）
- 游泳（有氧运动）
- 篮球（球类运动）
- 足球（球类运动）
- 举重（力量训练）
- 瑜伽（柔韧性训练）
- 乒乓球（球类运动）
- 羽毛球（球类运动）
- 骑行（有氧运动）
- 健身操（有氧运动）

## 常见问题

### Q: 运行程序时提示"连接数据库失败"
A: 请检查：
1. MySQL服务是否已启动
2. 用户名和密码是否正确
3. 端口3306是否可访问
4. 防火墙设置是否阻止连接

### Q: 如何修改数据库密码？
A: 编辑 `DatabaseUtil.java` 文件中的 `DB_PASSWORD` 常量

### Q: 如何重新创建数据库？
A: 
1. 删除现有数据库：`DROP DATABASE TianyiSport;`
2. 重新运行Main.java程序

## 依赖管理
项目使用Maven管理依赖，MySQL JDBC驱动版本为8.0.33。

如需更新依赖版本，请修改 `pom.xml` 文件。
