-- TianyiSport 数据库创建脚本
-- 创建日期: 2025-07-11
-- 描述: 创建TianyiSport运动管理数据库及基本表结构

-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS TianyiSport 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE TianyiSport;

-- 2. 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    full_name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 3. 创建运动项目表
CREATE TABLE IF NOT EXISTS sports (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '运动项目ID',
    name VARCHAR(100) NOT NULL COMMENT '运动项目名称',
    description TEXT COMMENT '运动项目描述',
    category VARCHAR(50) COMMENT '运动类别',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运动项目表';

-- 4. 创建运动记录表
CREATE TABLE IF NOT EXISTS sport_records (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    sport_id INT NOT NULL COMMENT '运动项目ID',
    duration_minutes INT COMMENT '运动时长(分钟)',
    calories_burned INT COMMENT '消耗卡路里',
    notes TEXT COMMENT '备注',
    record_date DATE NOT NULL COMMENT '运动日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运动记录表';

-- 5. 插入示例运动项目数据
INSERT IGNORE INTO sports (name, description, category) VALUES 
('跑步', '有氧运动，提高心肺功能', '有氧运动'),
('游泳', '全身运动，低冲击性', '有氧运动'),
('篮球', '团队运动，提高协调性', '球类运动'),
('足球', '世界第一运动', '球类运动'),
('举重', '力量训练，增强肌肉', '力量训练'),
('瑜伽', '身心平衡，提高柔韧性', '柔韧性训练'),
('乒乓球', '国球运动，锻炼反应能力', '球类运动'),
('羽毛球', '室内运动，全身协调', '球类运动'),
('骑行', '有氧运动，环保出行', '有氧运动'),
('健身操', '团体运动，音乐配合', '有氧运动');

-- 6. 创建竞彩足球表
CREATE TABLE IF NOT EXISTS jcTable (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    num VARCHAR(20) NOT NULL COMMENT '场次编号',
    match_time VARCHAR(20) NOT NULL COMMENT '比赛时间',
    match_id VARCHAR(20) NOT NULL UNIQUE COMMENT '比赛ID',
    league VARCHAR(100) NOT NULL COMMENT '联赛名称',
    home VARCHAR(100) NOT NULL COMMENT '主队名称',
    away VARCHAR(100) NOT NULL COMMENT '客队名称',

    -- 比赛结果相关字段
    half_score VARCHAR(10) COMMENT '半场比分',
    full_score VARCHAR(10) COMMENT '全场比分',

    -- 胜平负相关
    win_draw_lose_result VARCHAR(5) COMMENT '胜平负结果(1胜2平3负)',
    win_draw_lose_odds DECIMAL(6,3) COMMENT '胜平负赔率',

    -- 让球胜平负相关
    handicap VARCHAR(10) COMMENT '让球数',
    handicap_result VARCHAR(5) COMMENT '让球胜平负结果',
    handicap_odds DECIMAL(6,3) COMMENT '让球胜平负赔率',

    -- 比分相关
    score_result VARCHAR(10) COMMENT '比分结果',
    score_odds DECIMAL(6,3) COMMENT '比分赔率',

    -- 总进球数相关
    goal_result VARCHAR(5) COMMENT '总进球数结果',
    goal_odds DECIMAL(6,3) COMMENT '总进球数赔率',

    -- 半全场相关
    half_full_result VARCHAR(10) COMMENT '半全场结果',
    half_full_odds DECIMAL(6,3) COMMENT '半全场赔率',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩足球比赛数据表';

-- 3. 创建索引以提高查询性能
CREATE INDEX idx_jc_match_id ON jcTable(match_id);
CREATE INDEX idx_jc_league ON jcTable(league);
CREATE INDEX idx_jc_match_time ON jcTable(match_time);
CREATE INDEX idx_jc_num ON jcTable(num);

-- 4. 插入示例竞彩数据
INSERT IGNORE INTO jcTable (
    num, match_time, match_id, league, home, away,
    half_score, full_score, win_draw_lose_result, win_draw_lose_odds,
    handicap, handicap_result, handicap_odds,
    score_result, score_odds, goal_result, goal_odds,
    half_full_result, half_full_odds
) VALUES
('周六018', '06-01 00:45', '1282037', '挪超', '特罗姆瑟', '瓦勒伦加',
 '0-0', '2-1', '3', 1.700, '-1', '1', 3.300,
 '2:1', 7.000, '3', 3.500, '1-3', 4.000);

-- 5. 显示创建结果
SELECT 'TianyiSport竞彩足球数据库创建完成！' AS message;
SHOW TABLES;
