#!/bin/bash

# Jackson依赖问题解决脚本
# 用于解决com.fasterxml.jackson相关的依赖问题

echo "=== Jackson依赖问题解决脚本 ==="
echo

# 1. 清理Maven缓存
echo "1. 清理Maven缓存..."
mvn dependency:purge-local-repository -DmanualInclude="com.fasterxml.jackson.core:jackson-core,com.fasterxml.jackson.core:jackson-annotations,com.fasterxml.jackson.core:jackson-databind"

echo

# 2. 清理项目
echo "2. 清理项目..."
mvn clean

echo

# 3. 重新下载依赖
echo "3. 重新下载依赖..."
mvn dependency:resolve

echo

# 4. 编译项目
echo "4. 编译项目..."
mvn compile

echo

# 5. 验证依赖
echo "5. 验证Jackson依赖..."
mvn dependency:tree | grep jackson

echo
echo "=== 脚本执行完成 ==="
echo "如果仍有问题，请检查网络连接或Maven仓库配置"
