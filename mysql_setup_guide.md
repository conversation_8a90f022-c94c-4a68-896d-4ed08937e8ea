# MySQL连接问题解决指南

## 问题描述
遇到错误：`Access denied for user 'root'@'localhost'`

## 快速解决方案

### 方案1：重置MySQL root密码

#### macOS/Linux:
```bash
# 1. 停止MySQL服务
sudo brew services stop mysql  # macOS
# 或
sudo systemctl stop mysql     # Linux

# 2. 以安全模式启动MySQL
sudo mysqld_safe --skip-grant-tables &

# 3. 连接到MySQL（无需密码）
mysql -u root

# 4. 重置密码
USE mysql;
UPDATE user SET authentication_string=PASSWORD('821661664') WHERE User='root';
FLUSH PRIVILEGES;
EXIT;

# 5. 重启MySQL服务
sudo brew services start mysql  # macOS
# 或
sudo systemctl start mysql     # Linux
```

#### Windows:
```cmd
# 1. 以管理员身份打开命令提示符
# 2. 停止MySQL服务
net stop mysql

# 3. 创建初始化文件 C:\mysql-init.txt，内容：
ALTER USER 'root'@'localhost' IDENTIFIED BY '821661664';

# 4. 启动MySQL并执行初始化文件
mysqld --init-file=C:\mysql-init.txt

# 5. 重启MySQL服务
net start mysql
```

### 方案2：使用MySQL安全安装脚本
```bash
sudo mysql_secure_installation
```
按提示设置root密码为：`821661664`

### 方案3：创建新的数据库用户
```sql
-- 以root身份登录MySQL
mysql -u root -p

-- 创建新用户
CREATE USER 'tianyi'@'localhost' IDENTIFIED BY '821661664';

-- 授予所有权限
GRANT ALL PRIVILEGES ON *.* TO 'tianyi'@'localhost' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

然后修改 `DatabaseUtil.java` 中的用户名：
```java
private static final String DB_USERNAME = "tianyi";
```

## 测试连接

### 方法1：使用诊断工具
```bash
# 编译并运行诊断工具
javac -cp ".:target/classes" src/main/java/com/tianyi/util/MySQLDiagnostic.java
java -cp ".:target/classes" com.tianyi.util.MySQLDiagnostic
```

### 方法2：使用MySQL命令行测试
```bash
mysql -u root -p821661664 -h localhost -P 3306
```

如果能成功连接，说明配置正确。

## 常见错误及解决方法

### 错误1：`mysql: command not found`
**解决方法：**
```bash
# macOS
brew install mysql

# Ubuntu/Debian
sudo apt-get install mysql-server mysql-client

# CentOS/RHEL
sudo yum install mysql-server mysql
```

### 错误2：`Can't connect to local MySQL server`
**解决方法：**
```bash
# 检查MySQL服务状态
sudo systemctl status mysql    # Linux
brew services list | grep mysql # macOS

# 启动MySQL服务
sudo systemctl start mysql     # Linux
brew services start mysql      # macOS
```

### 错误3：`Unknown database 'TianyiSport'`
这是正常的，因为数据库还没有创建。程序会自动创建数据库。

## 验证步骤

1. **确认MySQL服务运行：**
   ```bash
   ps aux | grep mysql
   ```

2. **测试连接：**
   ```bash
   mysql -u root -p821661664
   ```

3. **运行Java程序：**
   ```bash
   mvn compile exec:java -Dexec.mainClass="Main"
   ```

## 如果仍然有问题

请运行诊断工具获取详细信息：
```bash
java -cp "target/classes" com.tianyi.util.MySQLDiagnostic
```

或者联系我提供更多帮助！
