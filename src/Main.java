import com.tianyi.database.DatabaseManager;
import com.tianyi.util.DatabaseUtil;

/**
 * TianyiPro 主程序
 * 包含TianyiSport数据库的创建和管理功能
 */
public class Main {
    public static void main(String[] args) {
        System.out.println("=== 欢迎使用 TianyiPro 运动管理系统 ===");
        System.out.println();

        // 检查数据库是否已存在
        if (DatabaseManager.databaseExists()) {
            System.out.println("TianyiSport数据库已存在！");
            System.out.println("正在测试数据库连接...");

            if (DatabaseUtil.testConnection()) {
                System.out.println("数据库连接正常！");
            } else {
                System.out.println("数据库连接失败，请检查MySQL服务是否启动。");
                return;
            }
        } else {
            System.out.println("TianyiSport数据库不存在，开始创建...");

            // 初始化数据库
            if (DatabaseManager.initializeDatabase()) {
                System.out.println("数据库初始化成功！");
            } else {
                System.out.println("数据库初始化失败，请检查MySQL配置。");
                System.out.println("请确保：");
                System.out.println("1. MySQL服务已启动");
                System.out.println("2. 用户名和密码正确（当前配置：root/空密码）");
                System.out.println("3. MySQL端口3306可访问");
                return;
            }
        }

        System.out.println();
        System.out.println("=== 数据库信息 ===");
        System.out.println("数据库名称: " + DatabaseUtil.getDatabaseName());
        System.out.println("数据库已包含以下表：");
        System.out.println("- users (用户表)");
        System.out.println("- sports (运动项目表)");
        System.out.println("- sport_records (运动记录表)");
        System.out.println();
        System.out.println("TianyiSport数据库创建完成，可以开始使用了！");
    }
}