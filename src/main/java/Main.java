import com.tianyi.dao.BuyTableType;
import com.tianyi.database.DatabaseManager;
import com.tianyi.util.*;
import com.tianyi.model.MatchData;

import java.util.ArrayList;
import java.util.List;

/**
 * TianyiPro 主程序
 * 包含TianyiSport数据库的创建和管理功能
 */
public class Main {

    public static void main(String[] args) {
//        createDatabase();
//        JCTableManager.dropJCTable();
//        DatabaseManager.createBasicTables();
//        JCDataUtil.showAllMatches();
//        JCDataUtil.showMatchesByDateRange("2025-01-01", "2025-12-31");
//        insertJCMatchData("2025", 12);

        ArrayList<MatchData> list = JCDataUtil.getMatchesByDate("2025-01-01");
        if (list.size() > 1) {
            MatchData fMatch = list.get(0);  // 第一场
            MatchData sMatch = list.get(1);  // 第二场
            MatchData e2Match = list.get(list.size()-2);  // 倒数第二场
            MatchData e1Match = list.get(list.size()-1);  // 倒数第一场

            BuyTableType fsType = BuyTableType.FS;
            BuyTableType feType = BuyTableType.FE;
            BuyTableType endType = BuyTableType.END;

            BuyDataUtils.insertBuyResultData(fsType, fMatch, sMatch, "2025年1月");
            BuyDataUtils.insertBuyResultData(feType, fMatch, e1Match, "2025年1月");
            BuyDataUtils.insertBuyResultData(endType, e1Match, e2Match, "2025年1月");
        }

    }

    private static void insertJCMatchData(String year, int endMonth) {

        for (int i = 1; i <= endMonth; i++) {
            System.out.println("正在处理第 " + i + " 个月份 (" + year + "年" + i + "月)");

            String timeStr = year + "年" + i + "月";
            String basePath = "/Users/<USER>/Desktop/JAVA/TianyiPro/sports_db/code/data/" + year + "/" + timeStr;

            try {
                // 处理当前月份的所有JSON文件
                processMonthData(basePath, timeStr, i);

                System.out.println("✅ 第 " + i + " 个月份处理完成！");

                // 如果不是最后一个月份，延时1秒
                if (i < endMonth) {
                    System.out.println("⏳ 延时1秒后继续处理下一个月份...");
                    Thread.sleep(1000); // 延时1秒
                    System.out.println();
                }

            } catch (InterruptedException e) {
                System.err.println("❌ 延时被中断: " + e.getMessage());
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                System.err.println("❌ 处理第 " + i + " 个月份时发生错误: " + e.getMessage());
                e.printStackTrace();

                // 询问是否继续处理下一个月份
                System.out.println("⚠️  是否继续处理下一个月份？继续执行...");
                if (i < endMonth) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 处理单个月份的数据
     */
    private static void processMonthData(String basePath, String timeStr, int monthNumber) {
        System.out.println("📁 扫描目录: " + basePath);

        List<String> list = JCFileUtil.getAllSubFiles(basePath);

        if (list == null || list.isEmpty()) {
            System.out.println("⚠️  目录中没有找到文件: " + basePath);
            return;
        }

        int fileCount = 0;
        int processedCount = 0;

        for (String item : list) {
            if (item.contains(".json") && item.contains("-") && !item.contains("ids")) {
                fileCount++;
                System.out.println("📄 文件路径: " + item);

                try {
                    processJsonFileWithTimeConversion(item, timeStr);
                    processedCount++;
                } catch (Exception e) {
                    System.err.println("❌ 处理文件失败: " + item + " - " + e.getMessage());
                }
            }
        }

        System.out.println("📊 月份 " + monthNumber + " 统计: 找到 " + fileCount + " 个文件，成功处理 " + processedCount + " 个文件");
    }

    public static void createDatabase() {
        // 检查数据库是否已存在
        if (DatabaseManager.databaseExists()) {
            System.out.println("TianyiSport数据库已存在！");
            System.out.println("正在测试数据库连接...");
            if (DatabaseUtil.testConnection()) {
                System.out.println("数据库连接正常！");
            } else {
                System.out.println("数据库连接失败，请检查MySQL服务是否启动。");
                return;
            }
        } else {
            System.out.println("TianyiSport数据库不存在，开始创建...");
            // 初始化数据库
            if (DatabaseManager.initializeDatabase()) {
                System.out.println("数据库初始化成功！");
            } else {
                System.out.println("数据库初始化失败，请检查MySQL配置。");
                System.out.println("请确保：");
                System.out.println("1. MySQL服务已启动");
                System.out.println("2. 用户名和密码正确");
                System.out.println("3. MySQL端口3306可访问");
                return;
            }
        }
    }

    /**
     * 处理JSON文件并进行时间转换
     */
    public static void processJsonFileWithTimeConversion(String path, String timeStr) {
        System.out.println("=== JSON文件处理和时间转换 ===");
        System.out.println("正在读取JSON文件: " + path);
        System.out.println();

        // 1. 验证JSON文件
        if (!JsonUtil.validateJsonFile(path)) {
            System.err.println("JSON文件验证失败，程序终止");
            return;
        }

        // 2. 读取JSON数据
        List<MatchData> matchList = JsonUtil.readMatchDataFromFile(path);
        if (matchList == null || matchList.isEmpty()) {
            System.err.println("无法读取JSON数据或数据为空:" + path);
            return;
        }

        System.out.println();

        JsonUtil.insertDetailedMatches(matchList, timeStr);
    }

    /**
     * 打印包含格式化时间的比赛摘要
     */
    private static void printMatchSummaryWithFormattedTime(String timeStr, List<MatchData> matchList) {
        System.out.println("=== 比赛摘要（含格式化时间） ===");
        System.out.println("总共 " + matchList.size() + " 场比赛");
        System.out.println();

        System.out.printf("%-10s %-20s %-10s %-15s %-15s %-10s %-10s%n",
                         "场次", "格式化时间", "联赛", "主队", "客队", "半场", "全场");
        System.out.println("=".repeat(110));

        for (MatchData match : matchList) {
            MatchData.MatchResult result = match.getResult();
            String formattedTime = TimeUtil.combineSimpleDateTime(timeStr, match.getMatchTime());

            System.out.printf("%-10s %-20s %-10s %-15s %-15s %-10s %-10s%n",
                match.getNum(),
                formattedTime,
                match.getLeague(),
                match.getHome(),
                match.getAway(),
                result != null ? result.getHalf() : "N/A",
                result != null ? result.getFull() : "N/A"
            );
        }
        System.out.println();
    }
}