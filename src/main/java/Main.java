
import com.tianyi.dao.BuyTableDAO;
import com.tianyi.database.DatabaseManager;
import com.tianyi.model.JCBuyResultModel;
import com.tianyi.util.*;
import com.tianyi.model.MatchData;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TianyiPro 主程序
 * 包含TianyiSport数据库的创建和管理功能
 */
public class Main {

    public static void main(String[] args) {
//        createDatabase();
//        JCTableManager.dropJCTable();
//        JCTableManager.creatJCTable();
//        JCDataUtil.showAllMatches();
//        JCDataUtil.showMatchesByDateRange("2025-07-01", "2025-07-01");
//        insertJCMatchData("2025", 12);

//        for (int i = 1; i < 13; i++) {
//            processYearMonthData(2020, i);
//        }
        queryYearMonthData(2023);
//        for (int i = 1; i < 8; i++) {
//            queryYearMonthData(2025, i);
//        }
    }

    private static void processYearMonthData(int year, int month) {
        System.out.println("开始处理" + year + "年" + month + "月的比赛数据...");

        // 获取该月的天数
        int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

        for (int day = 1; day <= daysInMonth; day++) {
            String dateStr = String.format("%d-%02d-%02d", year, month, day);
            System.out.println("=== 处理日期: " + dateStr + " ===");

            // 获取指定日期的所有比赛
            ArrayList<MatchData> list = JCDataUtil.getMatchesByDate(dateStr);

            // 检查是否有足够的比赛数据
            if (list.size() < 2) {
                System.out.println("日期 " + dateStr + " 的比赛数据不足，跳过处理");
                continue;
            }

            MatchData firstMatch = null;
            MatchData secondMatch = null;
            MatchData end1Match = null;
            MatchData end2Match = null;
            for (int i = 0; i < list.size() - 1; i++) {
                MatchData matchData1 = list.get(i);
                MatchData matchData2 = list.get(i + 1);
                if (i == 0) {
                    firstMatch = matchData1;
                    secondMatch = matchData2;
                    JCGoalBuyUtil.setGoalResultData2(dateStr, matchData1, matchData2, 0, 0, 1);
                    JCHalfFullBuyUtil.setHalfFullMatchResultData2(dateStr, matchData1, matchData2, 0, 0, 1);
                    JCHandicapBuyUtil.setHandicapResultData2(dateStr, matchData1, matchData2, 0, 0, 1);
                    JCScoreBuyUtil.setScoreResultData2(dateStr, matchData1, matchData2, 0, 0, 1);
                }
                if (list.size() > 2 && i == list.size() - 2) {
                    end2Match = matchData1;
                    end1Match = matchData2;
                    JCGoalBuyUtil.setGoalResultData2(dateStr, matchData1, matchData2, 1, 0, 0);
                    JCHalfFullBuyUtil.setHalfFullMatchResultData2(dateStr, matchData1, matchData2, 1, 0, 0);
                    JCHandicapBuyUtil.setHandicapResultData2(dateStr, matchData1, matchData2, 1, 0, 0);
                    JCScoreBuyUtil.setScoreResultData2(dateStr, matchData1, matchData2, 1, 0, 0);
                }
            }
            if (list.size() > 2 && end1Match != null) {
                // 处理FE数据
                JCHalfFullBuyUtil.setHalfFullMatchResultData2(dateStr, firstMatch, end1Match, 0, 1, 0);
                JCGoalBuyUtil.setGoalResultData2(dateStr, firstMatch, end1Match, 0, 1, 0);
                JCHandicapBuyUtil.setHandicapResultData2(dateStr, firstMatch, end1Match, 0, 1, 0);
                JCScoreBuyUtil.setScoreResultData2(dateStr, firstMatch, end1Match, 0, 1, 0);
            }
            if (end1Match != null) {
                // 处理3场
                JCHalfFullBuyUtil.setHalfFullMatchResultData3(dateStr, firstMatch, secondMatch, end1Match);
                JCGoalBuyUtil.setGoalResultData3(dateStr, firstMatch, secondMatch, end1Match);
                JCHandicapBuyUtil.setHandicapResultData3(dateStr, firstMatch, secondMatch, end1Match);
                JCScoreBuyUtil.setScoreResultData3(dateStr, firstMatch, secondMatch, end1Match);
                if (list.size() > 3) {
                    // 处理4场
                    JCHalfFullBuyUtil.setHalfFullMatchResultData4(dateStr, firstMatch, secondMatch, end2Match, end1Match);
                    JCGoalBuyUtil.setGoalResultData4(dateStr, firstMatch, secondMatch, end2Match, end1Match);
                    JCHandicapBuyUtil.setHandicapResultData4(dateStr, firstMatch, secondMatch, end2Match, end1Match);
                    JCScoreBuyUtil.setScoreResultData4(dateStr, firstMatch, secondMatch, end2Match, end1Match);
                }
            }

            try {
                System.out.println("⏳ 延时1秒后继续处理下一个...");
                Thread.sleep(100); // 延时0.1秒
            } catch (InterruptedException e) {
                System.err.println("❌ 延时被中断: " + e.getMessage());
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                System.err.println("❌");
            }
        }

        System.out.println(year + "年" + month + "月所有数据处理完成！");
    }

    private static void queryYearMonthData(int year) {
        System.out.println("======> 开始处理" + year + "年的数据结果...");
//        JCGoalBuyUtil.queryBuyResultByMonthData(year, month);
//        JCHalfFullBuyUtil.queryBuyResultByMonthData(year);
//        JCHalfFullBuyUtil.queryBuyResultByMonthDetailData(year);
        JCScoreBuyUtil.queryBuyResultByMonthData(year);
//        JCHandicapBuyUtil.queryBuyResultByMonthData(year, month);
//        JCHandicapBuyUtil.querySpecialBuyResultByMonthData(year, month);
    }

    private static void setCashMap(int day, Map<String, String> halfFullCashDateMap, Map<String, Integer> halfFullCashMap, String key, int cash) {
        if (cash > 0) {
            cash -= 100;
            halfFullCashDateMap.put(key, halfFullCashDateMap.get(key) + day + ",");
        }
        halfFullCashMap.put(key, halfFullCashMap.get(key) + cash);
    }

    private static void insertJCMatchData(String year, int endMonth) {

        for (int i = 1; i <= endMonth; i++) {
            System.out.println("正在处理第 " + i + " 个月份 (" + year + "年" + i + "月)");

            String timeStr = year + "年" + i + "月";
            String basePath = "/Users/<USER>/Desktop/JAVA/TianyiPro/sports_db/code/data/" + year + "/" + timeStr;

            try {
                // 处理当前月份的所有JSON文件
                processMonthData(basePath, timeStr, i);

                System.out.println("✅ 第 " + i + " 个月份处理完成！");

                // 如果不是最后一个月份，延时1秒
                if (i < endMonth) {
                    System.out.println("⏳ 延时1秒后继续处理下一个月份...");
                    Thread.sleep(1000); // 延时1秒
                    System.out.println();
                }

            } catch (InterruptedException e) {
                System.err.println("❌ 延时被中断: " + e.getMessage());
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                System.err.println("❌ 处理第 " + i + " 个月份时发生错误: " + e.getMessage());
                e.printStackTrace();

                // 询问是否继续处理下一个月份
                System.out.println("⚠️  是否继续处理下一个月份？继续执行...");
                if (i < endMonth) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 处理单个月份的数据
     */
    private static void processMonthData(String basePath, String timeStr, int monthNumber) {
        System.out.println("📁 扫描目录: " + basePath);

        List<String> list = JCFileUtil.getAllSubFiles(basePath);

        if (list == null || list.isEmpty()) {
            System.out.println("⚠️  目录中没有找到文件: " + basePath);
            return;
        }

        int fileCount = 0;
        int processedCount = 0;

        for (String item : list) {
            if (item.contains(".json") && item.contains("-") && !item.contains("ids")) {
                fileCount++;
                System.out.println("📄 文件路径: " + item);

                try {
                    processJsonFileWithTimeConversion(item, timeStr);
                    processedCount++;
                } catch (Exception e) {
                    System.err.println("❌ 处理文件失败: " + item + " - " + e.getMessage());
                }
            }
        }

        System.out.println("📊 月份 " + monthNumber + " 统计: 找到 " + fileCount + " 个文件，成功处理 " + processedCount + " 个文件");
    }

    public static void createDatabase() {
        // 检查数据库是否已存在
        if (DatabaseManager.databaseExists()) {
            System.out.println("TianyiSport数据库已存在！");
            System.out.println("正在测试数据库连接...");
            if (DatabaseUtil.testConnection()) {
                System.out.println("数据库连接正常！");
            } else {
                System.out.println("数据库连接失败，请检查MySQL服务是否启动。");
                return;
            }
        } else {
            System.out.println("TianyiSport数据库不存在，开始创建...");
            // 初始化数据库
            if (DatabaseManager.initializeDatabase()) {
                System.out.println("数据库初始化成功！");
            } else {
                System.out.println("数据库初始化失败，请检查MySQL配置。");
                System.out.println("请确保：");
                System.out.println("1. MySQL服务已启动");
                System.out.println("2. 用户名和密码正确");
                System.out.println("3. MySQL端口3306可访问");
                return;
            }
        }
    }

    /**
     * 处理JSON文件并进行时间转换
     */
    public static void processJsonFileWithTimeConversion(String path, String timeStr) {
        System.out.println("=== JSON文件处理和时间转换 ===");
        System.out.println("正在读取JSON文件: " + path);
        System.out.println();

        // 1. 验证JSON文件
        if (!JsonUtil.validateJsonFile(path)) {
            System.err.println("JSON文件验证失败，程序终止");
            return;
        }

        // 2. 读取JSON数据
        List<MatchData> matchList = JsonUtil.readMatchDataFromFile(path);
        if (matchList == null || matchList.isEmpty()) {
            System.err.println("无法读取JSON数据或数据为空:" + path);
            return;
        }

        System.out.println();

        JsonUtil.insertDetailedMatches(matchList, timeStr);
    }

    // 33  33
    private static Map<String, Object> getHalfFullStrong2ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("33-33", "11");
        map.put("33-00", "10");
        map.put("00-00", "00");
        map.put("00-33", "01");
        return  map;
    }

    // 13  13
    private static Map<String, Object> getHalfFullStrong20ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("13-13", "11");
        map.put("13-10", "10");
        map.put("10-10", "00");
        map.put("10-13", "01");
        return  map;
    }

    // 33  13
    private static Map<String, Object> getHalfFullStrong21ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("33-13", "11");
        map.put("33-10", "10");
        map.put("00-10", "00");
        map.put("00-13", "01");
        return  map;
    }

    // 13 33
    private static Map<String, Object> getHalfFullStrong22ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("13-33", "11");
        map.put("13-00", "10");
        map.put("10-00", "00");
        map.put("10-33", "01");
        return  map;
    }

    // 33  33
    private static Map<String, Object> getHalfFullWeek2ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("33-33", "00");
        map.put("33-00", "01");
        map.put("00-00", "11");
        map.put("00-33", "10");
        return  map;
    }

    // 13  13
    private static Map<String, Object> getHalfFullWeek20ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("13-13", "00");
        map.put("13-10", "01");
        map.put("10-10", "11");
        map.put("10-13", "10");
        return  map;
    }

    // 33  13
    private static Map<String, Object> getHalfFullWeek21ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("33-13", "00");
        map.put("33-10", "01");
        map.put("00-10", "11");
        map.put("00-13", "10");
        return  map;
    }

    // 13  33
    private static Map<String, Object> getHalfFullWeek22ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);
        map.put("cash", 0);
        map.put("13-33", "00");
        map.put("13-00", "01");
        map.put("10-00", "11");
        map.put("10-33", "10");
        return  map;
    }

    private static Map<String, Object> getHalfFullStrong3ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        return  map;
    }

    private static Map<String, Object> getHalfFullStrong4ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        return  map;
    }
}