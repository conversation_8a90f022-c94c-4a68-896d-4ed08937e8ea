package com.tianyi.dao;

import com.tianyi.model.JCBuyResultModel;
import com.tianyi.sql.JCBuyResultTableSQLStatements;
import com.tianyi.util.DatabaseUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class BuyTableDAO {

    public boolean searchHalfFullBuyResultByDate(String date) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHalfFullTableByDateSQL());

            // 设置参数值
            statement.setString(1, date);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    /**
     * 搜索
     */
    public boolean searchHalfFullBuyResultByDateAndNum(String date, String num) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHalfFullTableByDateAndNumSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchHalfFullBuyResultByDateAndNumAndFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHalfFullTableByDateAndNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

//    public JCBuyResultModel searchHalfFullBuyResultByDateAndMatchNum(String date, int matchNum) {
//        Connection connection = null;
//        PreparedStatement statement = null;
//        ResultSet resultSet = null;
//
//        try {
//            connection = DatabaseUtil.getConnection();
//            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHalfFullTableByDateAndNumSQL());
//
//            // 设置参数值
//            statement.setString(1, date);
//            statement.setInt(2, matchNum);
//
//            // 使用executeQuery而不是executeUpdate
//            resultSet = statement.executeQuery();
//
//            // 如果有结果，则表示数据存在
//            return resultSet.next();
//
//        } catch (SQLException e) {
//            System.err.println("搜索数据失败: " + e.getMessage());
//            return null;
//        } finally {
//            DatabaseUtil.closeResources(connection, statement, resultSet);
//        }
//    }

    public JCBuyResultModel searchHalfFullBuyResultByDateAndMatchNumAndFsFeEnd(String date, int matchNum, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        JCBuyResultModel jcBuyResultModel = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHalfFullTableByDateAndMatchNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setInt(2, matchNum);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            if (resultSet.next()) {
                jcBuyResultModel = getJcBuyResultModel(resultSet);
                jcBuyResultModel.setType(resultSet.getString("type"));
                jcBuyResultModel.setLastMatch(resultSet.getInt("lastMatch"));
                jcBuyResultModel.setFeMatch(resultSet.getInt("feMatch"));
                jcBuyResultModel.setFsMatch(resultSet.getInt("fsMatch"));
                jcBuyResultModel.setMatchNum(resultSet.getInt("matchNum"));
            }
            return jcBuyResultModel;

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return null;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    private JCBuyResultModel getJcBuyResultModel(ResultSet resultSet) throws SQLException {
        JCBuyResultModel jcBuyResultModel;
        jcBuyResultModel = new JCBuyResultModel();
        jcBuyResultModel.setJcDate(resultSet.getString("date_time"));
        jcBuyResultModel.setNum(resultSet.getString("num"));
        jcBuyResultModel.setResult(resultSet.getString("result"));
        jcBuyResultModel.setOdds(resultSet.getString("odds"));
        jcBuyResultModel.setTeamAndOdds(resultSet.getString("teamAndOdds"));
        return jcBuyResultModel;
    }

    public boolean searchHalfFullBuyResultByResult(String date, String num, String result, String type) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHalfFullTableByDateAndNumResultTypeSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);
            statement.setString(3, result);
            statement.setString(4, type);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchHandicapBuyResultByDateAndNum(String date, String num) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHandicapTableByDateAndNumSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchHandicapBuyResultByDateAndNumAndFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHandicapTableByDateAndNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public JCBuyResultModel searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(String date, int matchNum, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        JCBuyResultModel jcBuyResultModel = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHandicapTableByDateAndMatchNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setInt(2, matchNum);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            if (resultSet.next()) {
                jcBuyResultModel = getJcBuyResultModel(resultSet);
                jcBuyResultModel.setLastMatch(resultSet.getInt("lastMatch"));
                jcBuyResultModel.setFeMatch(resultSet.getInt("feMatch"));
                jcBuyResultModel.setFsMatch(resultSet.getInt("fsMatch"));
                jcBuyResultModel.setMatchNum(resultSet.getInt("matchNum"));
                jcBuyResultModel.setType(resultSet.getString("type"));
            }
            return jcBuyResultModel;

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return null;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchScoreBuyResultByDateAndNum(String date, String num) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchScoreTableByDateAndNumSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public JCBuyResultModel searchHandicapBuyResultByResult(String date, int num, String result, String type, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        JCBuyResultModel jcBuyResultModel = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchHandicapTableByDateAndNumResultTypeSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setInt(2, num);
            statement.setString(3, result);
            statement.setString(4, type);
            statement.setInt(5, fsMatch);
            statement.setInt(6, feMatch);
            statement.setInt(7, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            if (resultSet.next()) {
                jcBuyResultModel = getJcBuyResultModel(resultSet);
                jcBuyResultModel.setLastMatch(resultSet.getInt("lastMatch"));
                jcBuyResultModel.setFeMatch(resultSet.getInt("feMatch"));
                jcBuyResultModel.setFsMatch(resultSet.getInt("fsMatch"));
                jcBuyResultModel.setMatchNum(resultSet.getInt("matchNum"));
                jcBuyResultModel.setType(resultSet.getString("type"));
            }
            return jcBuyResultModel;

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return null;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchGoalBuyResultByDateAndNum(String date, String num) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchGoalTableByDateAndNumSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchGoalBuyResultByDateAndNumAndFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchGoalTableByDateAndNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public JCBuyResultModel searchGoalBuyResultByDateAndMatchNumAndFsFeEnd(String date, int matchNum, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        JCBuyResultModel jcBuyResultModel = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchGoalTableByDateAndMatchNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setInt(2, matchNum);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            if (resultSet.next()) {
                jcBuyResultModel = getJcBuyResultModel(resultSet);
                jcBuyResultModel.setLastMatch(resultSet.getInt("lastMatch"));
                jcBuyResultModel.setFeMatch(resultSet.getInt("feMatch"));
                jcBuyResultModel.setFsMatch(resultSet.getInt("fsMatch"));
                jcBuyResultModel.setMatchNum(resultSet.getInt("matchNum"));
            }
            return jcBuyResultModel;

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return null;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchScoreBuyResultByResult(String date, String num, String result, int type) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchGoalTableByDateAndNumResultSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);
            statement.setString(3, result);
            statement.setInt(4, type);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public boolean searchScoreBuyResultByDateAndNumAndFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchScoreTableByDateAndNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setString(2, num);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            return resultSet.next();

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    public JCBuyResultModel searchScoreBuyResultByDateAndMatchNumAndFsFeEnd(String date, int matchNum, int fsMatch, int feMatch, int endMatch) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        JCBuyResultModel jcBuyResultModel = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getSearchScoreTableByDateAndMatchNumAndFsFeEndSQL());

            // 设置参数值
            statement.setString(1, date);
            statement.setInt(2, matchNum);
            statement.setInt(3, fsMatch);
            statement.setInt(4, feMatch);
            statement.setInt(5, endMatch);

            // 使用executeQuery而不是executeUpdate
            resultSet = statement.executeQuery();

            // 如果有结果，则表示数据存在
            if (resultSet.next()) {
                jcBuyResultModel = getJcBuyResultModel(resultSet);
                jcBuyResultModel.setLastMatch(resultSet.getInt("lastMatch"));
                jcBuyResultModel.setFeMatch(resultSet.getInt("feMatch"));
                jcBuyResultModel.setFsMatch(resultSet.getInt("fsMatch"));
                jcBuyResultModel.setMatchNum(resultSet.getInt("matchNum"));
            }
            return jcBuyResultModel;

        } catch (SQLException e) {
            System.err.println("搜索数据失败: " + e.getMessage());
            return null;
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }

    /**
     * 插入半全场表
     */
    public void insertHalfFullBuyResultData(String date, String num, String result, String odds, String teamAndOdds, String type, int lastMatch, int feMatch, int fsMatch, int matchNum) {

        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getInsertHalfFullTableSQL());

            insertBuyResultData(date, num, result, odds, teamAndOdds, type, lastMatch, feMatch, fsMatch, matchNum, statement);

        } catch (SQLException e) {
            System.err.println("❌ 插入数据失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

    private void insertBuyResultData(String date, String num, String result, String odds, String teamAndOdds, String type, int lastMatch, int feMatch, int fsMatch, int matchNum, PreparedStatement statement) throws SQLException {
        int paramIndex = 1;

        statement.setString(paramIndex++, date);
        statement.setString(paramIndex++, num);
        statement.setString(paramIndex++, result);
        statement.setString(paramIndex++, odds);
        statement.setString(paramIndex++, teamAndOdds);
        if (type != null) {
            statement.setString(paramIndex++, type);
        }
        statement.setInt(paramIndex++, lastMatch);
        statement.setInt(paramIndex++, feMatch);
        statement.setInt(paramIndex++, fsMatch);
        statement.setInt(paramIndex++, matchNum);

        int insertResult = statement.executeUpdate();

        if (insertResult > 0) {
            System.out.println("✅ 插入数据成功");
        } else {
            System.out.println("❌ 插入数据失败");
        }
    }

    /**
     * 插入让球表
     */
    public void insertHandicapBuyResultData(String date, String num, String result, String odds, String teamAndOdds, String type, int lastMatch, int feMatch, int fsMatch, int matchNum) {

        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getInsertHandicapTableSQL());

            insertBuyResultData(date, num, result, odds, teamAndOdds, type, lastMatch, feMatch, fsMatch, matchNum, statement);

        } catch (SQLException e) {
            System.err.println("❌ 插入数据失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

    /**
     * 插入大小球表
     */
    public void insertGoalBuyResultData(String date, String num, String result, String odds, String teamAndOdds, int lastMatch, int feMatch, int fsMatch, int matchNum) {

        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getInsertGoalTableSQL());

            insertBuyResultData(date, num, result, odds, teamAndOdds, null, lastMatch, feMatch, fsMatch, matchNum, statement);

        } catch (SQLException e) {
            System.err.println("❌ 插入数据失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

    public void insertScoreBuyResultData(String date, String num, String result, String odds, String teamAndOdds, int lastMatch, int feMatch, int fsMatch, int matchNum) {

        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCBuyResultTableSQLStatements.getInsertScoreTableSQL());

            insertBuyResultData(date, num, result, odds, teamAndOdds, null, lastMatch, feMatch, fsMatch, matchNum, statement);

        } catch (SQLException e) {
            System.err.println("❌ 插入数据失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
}
