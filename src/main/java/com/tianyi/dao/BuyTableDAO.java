package com.tianyi.dao;

import com.tianyi.sql.JCEndResultSQLStatements;
import com.tianyi.sql.JCFEResultSQLStatements;
import com.tianyi.sql.JCFSResultSQLStatements;
import com.tianyi.util.DatabaseUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class BuyTableDAO {

    /**
     * 根据类型获取对应的插入SQL语句
     */
    private String getInsertSQLByType(BuyTableType type) {
        return switch (type) {
            case FS -> JCFSResultSQLStatements.getInsertFSBuyResultTableSQL();
            case FE ->
                // 注意：你需要在JCFEResultSQLStatements类中添加对应的INSERT语句和获取方法
                    JCFEResultSQLStatements.getCreateFETableSQL(); // 这里需要你添加对应的获取方法
            case END ->
                // 注意：你需要在JCEndResultSQLStatements类中添加对应的INSERT语句和获取方法
                    JCEndResultSQLStatements.getCreateEndTableSQL(); // 这里需要你添加对应的获取方法
            default -> throw new IllegalArgumentException("不支持的表类型: " + type);
        };
    }

    /**
     * 根据类型获取对应的插入SQL语句
     */
    private String getSearchSQLByType(BuyTableType type) {
        return switch (type) {
            case FS -> JCFSResultSQLStatements.getSelectFSMatchByJCDateSQL();
            case FE ->
                // 注意：你需要在JCFEResultSQLStatements类中添加对应的INSERT语句和获取方法
                    JCFEResultSQLStatements.getSelectFEMatchByJCDateSQL(); // 这里需要你添加对应的获取方法
            case END ->
                // 注意：你需要在JCEndResultSQLStatements类中添加对应的INSERT语句和获取方法
                    JCEndResultSQLStatements.getSelectENDMatchByJCDateSQL(); // 这里需要你添加对应的获取方法
            default -> throw new IllegalArgumentException("不支持的表类型: " + type);
        };
    }

    /**
     * 插入竞彩固定场次购买结果数据
     */
    public boolean insertBuyResult(BuyTableType type, String jcTime, String fNum, String sNum,
                                   // 胜平负玩法
                                   String winLoseCash11,

                                   // 让球玩法
                                   String handicapCash33,
                                   String handicapCash31,
                                   String handicapCash30,
                                   String handicapCash13,
                                   String handicapCash11,
                                   String handicapCash10,
                                   String handicapCash03,
                                   String handicapCash01,
                                   String handicapCash00,

                                   // 半全场玩法
                                   String halfFullCash3333,
                                   String halfFullCash3331,
                                   String halfFullCash3313,
                                   String halfFullCash3311,
                                   String halfFullCash3310,
                                   String halfFullCash3301,
                                   String halfFullCash3300,

                                   String halfFullCash3133,
                                   String halfFullCash3131,
                                   String halfFullCash3113,
                                   String halfFullCash3111,
                                   String halfFullCash3110,
                                   String halfFullCash3101,
                                   String halfFullCash3100,

                                   String halfFullCash1333,
                                   String halfFullCash1331,
                                   String halfFullCash1313,
                                   String halfFullCash1311,
                                   String halfFullCash1310,
                                   String halfFullCash1301,
                                   String halfFullCash1300,

                                   String halfFullCash1133,
                                   String halfFullCash1131,
                                   String halfFullCash1113,
                                   String halfFullCash1111,
                                   String halfFullCash1110,
                                   String halfFullCash1101,
                                   String halfFullCash1100,

                                   String halfFullCash1033,
                                   String halfFullCash1031,
                                   String halfFullCash1013,
                                   String halfFullCash1011,
                                   String halfFullCash1010,
                                   String halfFullCash1001,
                                   String halfFullCash1000,

                                   String halfFullCash0133,
                                   String halfFullCash0131,
                                   String halfFullCash0113,
                                   String halfFullCash0111,
                                   String halfFullCash0110,
                                   String halfFullCash0101,
                                   String halfFullCash0100,

                                   String halfFullCash0033,
                                   String halfFullCash0031,
                                   String halfFullCash0013,
                                   String halfFullCash0011,
                                   String halfFullCash0010,
                                   String halfFullCash0001,
                                   String halfFullCash0000,

                                   // 总进球数玩法
                                   String scoreCash00,
                                   String scoreCash01,
                                   String scoreCash02,
                                   String scoreCash03,
                                   String scoreCash04,
                                   String scoreCash10,
                                   String scoreCash11,
                                   String scoreCash12,
                                   String scoreCash13,
                                   String scoreCash14,
                                   String scoreCash20,
                                   String scoreCash21,
                                   String scoreCash22,
                                   String scoreCash23,
                                   String scoreCash24,
                                   String scoreCash30,
                                   String scoreCash31,
                                   String scoreCash32,
                                   String scoreCash33,
                                   String scoreCash34,
                                   String scoreCash40,
                                   String scoreCash41,
                                   String scoreCash42,
                                   String scoreCash43,
                                   String scoreCash44,

                                   // 让球+平玩法
                                   String handicapDrawCash31,
                                   String handicapDrawCash101,
                                   String handicapDrawCash01,
                                   String handicapDrawCash13,
                                   String handicapDrawCash011,
                                   String handicapDrawCash10) {

        Connection connection = null;
        PreparedStatement statement = null;


        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(getInsertSQLByType(type));

            // 设置参数值
            int index = 1;
            statement.setString(index++, jcTime);
            statement.setString(index++, fNum);
            statement.setString(index++, sNum);

            // 设置胜平负玩法参数
            statement.setString(index++, winLoseCash11);

            // 设置让球玩法参数
            statement.setString(index++, handicapCash33);
            statement.setString(index++, handicapCash31);
            statement.setString(index++, handicapCash30);
            statement.setString(index++, handicapCash13);
            statement.setString(index++, handicapCash11);
            statement.setString(index++, handicapCash10);
            statement.setString(index++, handicapCash03);
            statement.setString(index++, handicapCash01);
            statement.setString(index++, handicapCash00);

            // 设置半全场玩法参数
            statement.setString(index++, halfFullCash3333);
            statement.setString(index++, halfFullCash3331);
            statement.setString(index++, halfFullCash3313);
            statement.setString(index++, halfFullCash3311);
            statement.setString(index++, halfFullCash3310);
            statement.setString(index++, halfFullCash3301);
            statement.setString(index++, halfFullCash3300);

            statement.setString(index++, halfFullCash3133);
            statement.setString(index++, halfFullCash3131);
            statement.setString(index++, halfFullCash3113);
            statement.setString(index++, halfFullCash3111);
            statement.setString(index++, halfFullCash3110);
            statement.setString(index++, halfFullCash3101);
            statement.setString(index++, halfFullCash3100);

            statement.setString(index++, halfFullCash1333);
            statement.setString(index++, halfFullCash1331);
            statement.setString(index++, halfFullCash1313);
            statement.setString(index++, halfFullCash1311);
            statement.setString(index++, halfFullCash1310);
            statement.setString(index++, halfFullCash1301);
            statement.setString(index++, halfFullCash1300);

            statement.setString(index++, halfFullCash1133);
            statement.setString(index++, halfFullCash1131);
            statement.setString(index++, halfFullCash1113);
            statement.setString(index++, halfFullCash1111);
            statement.setString(index++, halfFullCash1110);
            statement.setString(index++, halfFullCash1101);
            statement.setString(index++, halfFullCash1100);

            statement.setString(index++, halfFullCash1033);
            statement.setString(index++, halfFullCash1031);
            statement.setString(index++, halfFullCash1013);
            statement.setString(index++, halfFullCash1011);
            statement.setString(index++, halfFullCash1010);
            statement.setString(index++, halfFullCash1001);
            statement.setString(index++, halfFullCash1000);

            statement.setString(index++, halfFullCash0133);
            statement.setString(index++, halfFullCash0131);
            statement.setString(index++, halfFullCash0113);
            statement.setString(index++, halfFullCash0111);
            statement.setString(index++, halfFullCash0110);
            statement.setString(index++, halfFullCash0101);
            statement.setString(index++, halfFullCash0100);

            statement.setString(index++, halfFullCash0033);
            statement.setString(index++, halfFullCash0031);
            statement.setString(index++, halfFullCash0013);
            statement.setString(index++, halfFullCash0011);
            statement.setString(index++, halfFullCash0010);
            statement.setString(index++, halfFullCash0001);
            statement.setString(index++, halfFullCash0000);

            // 设置总进球数玩法参数
            statement.setString(index++, scoreCash00);
            statement.setString(index++, scoreCash01);
            statement.setString(index++, scoreCash02);
            statement.setString(index++, scoreCash03);
            statement.setString(index++, scoreCash04);
            statement.setString(index++, scoreCash10);
            statement.setString(index++, scoreCash11);
            statement.setString(index++, scoreCash12);
            statement.setString(index++, scoreCash13);
            statement.setString(index++, scoreCash14);
            statement.setString(index++, scoreCash20);
            statement.setString(index++, scoreCash21);
            statement.setString(index++, scoreCash22);
            statement.setString(index++, scoreCash23);
            statement.setString(index++, scoreCash24);
            statement.setString(index++, scoreCash30);
            statement.setString(index++, scoreCash31);
            statement.setString(index++, scoreCash32);
            statement.setString(index++, scoreCash33);
            statement.setString(index++, scoreCash34);
            statement.setString(index++, scoreCash40);
            statement.setString(index++, scoreCash41);
            statement.setString(index++, scoreCash42);
            statement.setString(index++, scoreCash43);
            statement.setString(index++, scoreCash44);

            // 设置让球+平玩法参数
            statement.setString(index++, handicapDrawCash31);
            statement.setString(index++, handicapDrawCash101);
            statement.setString(index++, handicapDrawCash01);
            statement.setString(index++, handicapDrawCash13);
            statement.setString(index++, handicapDrawCash011);
            statement.setString(index++, handicapDrawCash10);

            int result = statement.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("插入竞彩固定场次购买结果数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

    /**
     * 插入竞彩固定场次购买结果数据
     */
    public boolean searchBuyResult(BuyTableType type, String jcDate) {

        Connection connection = null;
        PreparedStatement statement = null;


        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(getSearchSQLByType(type));

            // 设置参数值
            statement.setString(1, jcDate);

            int result = statement.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("搜索竞彩固定场次购买结果数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
}
