package com.tianyi.dao;

import com.tianyi.sql.JCEndResultSQLStatements;
import com.tianyi.sql.JCFEResultSQLStatements;
import com.tianyi.sql.JCFSResultSQLStatements;
import com.tianyi.util.DatabaseUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class BuyTableDAO {

    /**
     * 根据类型获取对应的插入SQL语句
     */
    private String getInsertSQLByType(BuyTableType type) {
        return switch (type) {
            case FS -> JCFSResultSQLStatements.getInsertFSBuyResultTableSQL();
            case FE ->
                // 注意：你需要在JCFEResultSQLStatements类中添加对应的INSERT语句和获取方法
                    JCFEResultSQLStatements.getCreateFETableSQL(); // 这里需要你添加对应的获取方法
            case END ->
                // 注意：你需要在JCEndResultSQLStatements类中添加对应的INSERT语句和获取方法
                    JCEndResultSQLStatements.getCreateEndTableSQL(); // 这里需要你添加对应的获取方法
            default -> throw new IllegalArgumentException("不支持的表类型: " + type);
        };
    }

    /**
     * 插入竞彩固定场次购买结果数据
     */
    public boolean insertBuyResult(BuyTableType type, String jcTime, String fNum, String sNum,
                                   // 胜平负玩法
                                   String winLoseCash33, String winLoseOdds33,
                                   String winLoseCash31, String winLoseOdds31,
                                   String winLoseCash30, String winLoseOdds30,
                                   String winLoseCash13, String winLoseOdds13,
                                   String winLoseCash11, String winLoseOdds11,
                                   String winLoseCash10, String winLoseOdds10,
                                   String winLoseCash03, String winLoseOdds03,
                                   String winLoseCash01, String winLoseOdds01,
                                   String winLoseCash00, String winLoseOdds00,

                                   // 让球玩法
                                   String handicapCash33, String handicapOdds33,
                                   String handicapCash31, String handicapOdds31,
                                   String handicapCash30, String handicapOdds30,
                                   String handicapCash13, String handicapOdds13,
                                   String handicapCash11, String handicapOdds11,
                                   String handicapCash10, String handicapOdds10,
                                   String handicapCash03, String handicapOdds03,
                                   String handicapCash01, String handicapOdds01,
                                   String handicapCash00, String handicapOdds00,

                                   // 半全场玩法
                                   String halfFullCash3333, String halfFullOdds3333,
                                   String halfFullCash3331, String halfFullOdds3331,
                                   String halfFullCash3313, String halfFullOdds3313,
                                   String halfFullCash3311, String halfFullOdds3311,
                                   String halfFullCash3310, String halfFullOdds3310,
                                   String halfFullCash3301, String halfFullOdds3301,
                                   String halfFullCash3300, String halfFullOdds3300,

                                   String halfFullCash3133, String halfFullOdds3133,
                                   String halfFullCash3131, String halfFullOdds3131,
                                   String halfFullCash3113, String halfFullOdds3113,
                                   String halfFullCash3111, String halfFullOdds3111,
                                   String halfFullCash3110, String halfFullOdds3110,
                                   String halfFullCash3101, String halfFullOdds3101,
                                   String halfFullCash3100, String halfFullOdds3100,

                                   String halfFullCash1333, String halfFullOdds1333,
                                   String halfFullCash1331, String halfFullOdds1331,
                                   String halfFullCash1313, String halfFullOdds1313,
                                   String halfFullCash1311, String halfFullOdds1311,
                                   String halfFullCash1310, String halfFullOdds1310,
                                   String halfFullCash1301, String halfFullOdds1301,
                                   String halfFullCash1300, String halfFullOdds1300,

                                   String halfFullCash1133, String halfFullOdds1133,
                                   String halfFullCash1131, String halfFullOdds1131,
                                   String halfFullCash1113, String halfFullOdds1113,
                                   String halfFullCash1111, String halfFullOdds1111,
                                   String halfFullCash1110, String halfFullOdds1110,
                                   String halfFullCash1101, String halfFullOdds1101,
                                   String halfFullCash1100, String halfFullOdds1100,

                                   String halfFullCash1033, String halfFullOdds1033,
                                   String halfFullCash1031, String halfFullOdds1031,
                                   String halfFullCash1013, String halfFullOdds1013,
                                   String halfFullCash1011, String halfFullOdds1011,
                                   String halfFullCash1010, String halfFullOdds1010,
                                   String halfFullCash1001, String halfFullOdds1001,
                                   String halfFullCash1000, String halfFullOdds1000,

                                   String halfFullCash0133, String halfFullOdds0133,
                                   String halfFullCash0131, String halfFullOdds0131,
                                   String halfFullCash0113, String halfFullOdds0113,
                                   String halfFullCash0111, String halfFullOdds0111,
                                   String halfFullCash0110, String halfFullOdds0110,
                                   String halfFullCash0101, String halfFullOdds0101,
                                   String halfFullCash0100, String halfFullOdds0100,

                                   String halfFullCash0033, String halfFullOdds0033,
                                   String halfFullCash0031, String halfFullOdds0031,
                                   String halfFullCash0013, String halfFullOdds0013,
                                   String halfFullCash0011, String halfFullOdds0011,
                                   String halfFullCash0010, String halfFullOdds0010,
                                   String halfFullCash0001, String halfFullOdds0001,
                                   String halfFullCash0000, String halfFullOdds0000,

                                   // 总进球数玩法
                                   String scoreCash11, String scoreOdds11,
                                   String scoreCash12, String scoreOdds12,
                                   String scoreCash13, String scoreOdds13,
                                   String scoreCash14, String scoreOdds14,
                                   String scoreCash21, String scoreOdds21,
                                   String scoreCash22, String scoreOdds22,
                                   String scoreCash23, String scoreOdds23,
                                   String scoreCash24, String scoreOdds24,
                                   String scoreCash31, String scoreOdds31,
                                   String scoreCash32, String scoreOdds32,
                                   String scoreCash33, String scoreOdds33,
                                   String scoreCash34, String scoreOdds34,
                                   String scoreCash41, String scoreOdds41,
                                   String scoreCash42, String scoreOdds42,
                                   String scoreCash43, String scoreOdds43,
                                   String scoreCash44, String scoreOdds44,

                                   // 让球+平玩法
                                   String handicapDrawCash31, String handicapDrawOdds31,
                                   String handicapDrawCash101, String handicapDrawOdds101,
                                   String handicapDrawCash01, String handicapDrawOdds01,
                                   String handicapDrawCash13, String handicapDrawOdds13,
                                   String handicapDrawCash011, String handicapDrawOdds011,
                                   String handicapDrawCash10, String handicapDrawOdds10,

                                   // 让球+平玩法(特殊)
                                   String handicapDrawSCash31, String handicapDrawSOdds31,
                                   String handicapDrawSCash101, String handicapDrawSOdds101,
                                   String handicapDrawSCash01, String handicapDrawSOdds01,
                                   String handicapDrawSCash13, String handicapDrawSOdds13,
                                   String handicapDrawSCash011, String handicapDrawSOdds011,
                                   String handicapDrawSCash10, String handicapDrawSOdds10) {

        Connection connection = null;
        PreparedStatement statement = null;


        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(getInsertSQLByType(type));

            // 设置参数值
            int index = 1;
            statement.setString(index++, jcTime);
            statement.setString(index++, fNum);
            statement.setString(index++, sNum);

            // 设置胜平负玩法参数
            statement.setString(index++, winLoseCash33);
            statement.setString(index++, winLoseOdds33);
            statement.setString(index++, winLoseCash31);
            statement.setString(index++, winLoseOdds31);
            statement.setString(index++, winLoseCash30);
            statement.setString(index++, winLoseOdds30);
            statement.setString(index++, winLoseCash13);
            statement.setString(index++, winLoseOdds13);
            statement.setString(index++, winLoseCash11);
            statement.setString(index++, winLoseOdds11);
            statement.setString(index++, winLoseCash10);
            statement.setString(index++, winLoseOdds10);
            statement.setString(index++, winLoseCash03);
            statement.setString(index++, winLoseOdds03);
            statement.setString(index++, winLoseCash01);
            statement.setString(index++, winLoseOdds01);
            statement.setString(index++, winLoseCash00);
            statement.setString(index++, winLoseOdds00);

            // 设置让球玩法参数
            statement.setString(index++, handicapCash33);
            statement.setString(index++, handicapOdds33);
            statement.setString(index++, handicapCash31);
            statement.setString(index++, handicapOdds31);
            statement.setString(index++, handicapCash30);
            statement.setString(index++, handicapOdds30);
            statement.setString(index++, handicapCash13);
            statement.setString(index++, handicapOdds13);
            statement.setString(index++, handicapCash11);
            statement.setString(index++, handicapOdds11);
            statement.setString(index++, handicapCash10);
            statement.setString(index++, handicapOdds10);
            statement.setString(index++, handicapCash03);
            statement.setString(index++, handicapOdds03);
            statement.setString(index++, handicapCash01);
            statement.setString(index++, handicapOdds01);
            statement.setString(index++, handicapCash00);
            statement.setString(index++, handicapOdds00);

            // 设置半全场玩法参数
            statement.setString(index++, halfFullCash3333);
            statement.setString(index++, halfFullOdds3333);
            statement.setString(index++, halfFullCash3331);
            statement.setString(index++, halfFullOdds3331);
            statement.setString(index++, halfFullCash3313);
            statement.setString(index++, halfFullOdds3313);
            statement.setString(index++, halfFullCash3311);
            statement.setString(index++, halfFullOdds3311);
            statement.setString(index++, halfFullCash3310);
            statement.setString(index++, halfFullOdds3310);
            statement.setString(index++, halfFullCash3301);
            statement.setString(index++, halfFullOdds3301);
            statement.setString(index++, halfFullCash3300);
            statement.setString(index++, halfFullOdds3300);

            statement.setString(index++, halfFullCash3133);
            statement.setString(index++, halfFullOdds3133);
            statement.setString(index++, halfFullCash3131);
            statement.setString(index++, halfFullOdds3131);
            statement.setString(index++, halfFullCash3113);
            statement.setString(index++, halfFullOdds3113);
            statement.setString(index++, halfFullCash3111);
            statement.setString(index++, halfFullOdds3111);
            statement.setString(index++, halfFullCash3110);
            statement.setString(index++, halfFullOdds3110);
            statement.setString(index++, halfFullCash3101);
            statement.setString(index++, halfFullOdds3101);
            statement.setString(index++, halfFullCash3100);
            statement.setString(index++, halfFullOdds3100);

            statement.setString(index++, halfFullCash1333);
            statement.setString(index++, halfFullOdds1333);
            statement.setString(index++, halfFullCash1331);
            statement.setString(index++, halfFullOdds1331);
            statement.setString(index++, halfFullCash1313);
            statement.setString(index++, halfFullOdds1313);
            statement.setString(index++, halfFullCash1311);
            statement.setString(index++, halfFullOdds1311);
            statement.setString(index++, halfFullCash1310);
            statement.setString(index++, halfFullOdds1310);
            statement.setString(index++, halfFullCash1301);
            statement.setString(index++, halfFullOdds1301);
            statement.setString(index++, halfFullCash1300);
            statement.setString(index++, halfFullOdds1300);

            statement.setString(index++, halfFullCash1133);
            statement.setString(index++, halfFullOdds1133);
            statement.setString(index++, halfFullCash1131);
            statement.setString(index++, halfFullOdds1131);
            statement.setString(index++, halfFullCash1113);
            statement.setString(index++, halfFullOdds1113);
            statement.setString(index++, halfFullCash1111);
            statement.setString(index++, halfFullOdds1111);
            statement.setString(index++, halfFullCash1110);
            statement.setString(index++, halfFullOdds1110);
            statement.setString(index++, halfFullCash1101);
            statement.setString(index++, halfFullOdds1101);
            statement.setString(index++, halfFullCash1100);
            statement.setString(index++, halfFullOdds1100);

            statement.setString(index++, halfFullCash1033);
            statement.setString(index++, halfFullOdds1033);
            statement.setString(index++, halfFullCash1031);
            statement.setString(index++, halfFullOdds1031);
            statement.setString(index++, halfFullCash1013);
            statement.setString(index++, halfFullOdds1013);
            statement.setString(index++, halfFullCash1011);
            statement.setString(index++, halfFullOdds1011);
            statement.setString(index++, halfFullCash1010);
            statement.setString(index++, halfFullOdds1010);
            statement.setString(index++, halfFullCash1001);
            statement.setString(index++, halfFullOdds1001);
            statement.setString(index++, halfFullCash1000);
            statement.setString(index++, halfFullOdds1000);

            statement.setString(index++, halfFullCash0133);
            statement.setString(index++, halfFullOdds0133);
            statement.setString(index++, halfFullCash0131);
            statement.setString(index++, halfFullOdds0131);
            statement.setString(index++, halfFullCash0113);
            statement.setString(index++, halfFullOdds0113);
            statement.setString(index++, halfFullCash0111);
            statement.setString(index++, halfFullOdds0111);
            statement.setString(index++, halfFullCash0110);
            statement.setString(index++, halfFullOdds0110);
            statement.setString(index++, halfFullCash0101);
            statement.setString(index++, halfFullOdds0101);
            statement.setString(index++, halfFullCash0100);
            statement.setString(index++, halfFullOdds0100);

            statement.setString(index++, halfFullCash0033);
            statement.setString(index++, halfFullOdds0033);
            statement.setString(index++, halfFullCash0031);
            statement.setString(index++, halfFullOdds0031);
            statement.setString(index++, halfFullCash0013);
            statement.setString(index++, halfFullOdds0013);
            statement.setString(index++, halfFullCash0011);
            statement.setString(index++, halfFullOdds0011);
            statement.setString(index++, halfFullCash0010);
            statement.setString(index++, halfFullOdds0010);
            statement.setString(index++, halfFullCash0001);
            statement.setString(index++, halfFullOdds0001);
            statement.setString(index++, halfFullCash0000);
            statement.setString(index++, halfFullOdds0000);

            // 设置总进球数玩法参数
            statement.setString(index++, scoreCash11);
            statement.setString(index++, scoreOdds11);
            statement.setString(index++, scoreCash12);
            statement.setString(index++, scoreOdds12);
            statement.setString(index++, scoreCash13);
            statement.setString(index++, scoreOdds13);
            statement.setString(index++, scoreCash14);
            statement.setString(index++, scoreOdds14);
            statement.setString(index++, scoreCash21);
            statement.setString(index++, scoreOdds21);
            statement.setString(index++, scoreCash22);
            statement.setString(index++, scoreOdds22);
            statement.setString(index++, scoreCash23);
            statement.setString(index++, scoreOdds23);
            statement.setString(index++, scoreCash24);
            statement.setString(index++, scoreOdds24);
            statement.setString(index++, scoreCash31);
            statement.setString(index++, scoreOdds31);
            statement.setString(index++, scoreCash32);
            statement.setString(index++, scoreOdds32);
            statement.setString(index++, scoreCash33);
            statement.setString(index++, scoreOdds33);
            statement.setString(index++, scoreCash34);
            statement.setString(index++, scoreOdds34);
            statement.setString(index++, scoreCash41);
            statement.setString(index++, scoreOdds41);
            statement.setString(index++, scoreCash42);
            statement.setString(index++, scoreOdds42);
            statement.setString(index++, scoreCash43);
            statement.setString(index++, scoreOdds43);
            statement.setString(index++, scoreCash44);
            statement.setString(index++, scoreOdds44);

            // 设置让球+平玩法参数
            statement.setString(index++, handicapDrawCash31);
            statement.setString(index++, handicapDrawOdds31);
            statement.setString(index++, handicapDrawCash101);
            statement.setString(index++, handicapDrawOdds101);
            statement.setString(index++, handicapDrawCash01);
            statement.setString(index++, handicapDrawOdds01);
            statement.setString(index++, handicapDrawCash13);
            statement.setString(index++, handicapDrawOdds13);
            statement.setString(index++, handicapDrawCash011);
            statement.setString(index++, handicapDrawOdds011);
            statement.setString(index++, handicapDrawCash10);
            statement.setString(index++, handicapDrawOdds10);

            // 设置让球+平玩法(特殊)参数
            statement.setString(index++, handicapDrawSCash31);
            statement.setString(index++, handicapDrawSOdds31);
            statement.setString(index++, handicapDrawSCash101);
            statement.setString(index++, handicapDrawSOdds101);
            statement.setString(index++, handicapDrawSCash01);
            statement.setString(index++, handicapDrawSOdds01);
            statement.setString(index++, handicapDrawSCash13);
            statement.setString(index++, handicapDrawSOdds13);
            statement.setString(index++, handicapDrawSCash011);
            statement.setString(index++, handicapDrawSOdds011);
            statement.setString(index++, handicapDrawSCash10);
            statement.setString(index++, handicapDrawSOdds10);

            int result = statement.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("插入竞彩固定场次购买结果数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
}
