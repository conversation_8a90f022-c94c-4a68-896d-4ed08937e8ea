package com.tianyi.dao;

import com.tianyi.sql.JCTableSQLStatements;
import com.tianyi.util.DatabaseUtil;
import java.sql.*;

/**
 * 竞彩查询数据访问对象
 * 专门用于场次编号与竞彩时间相关的查询操作
 */
public class JCQueryDAO {
    /**
     * 根据场次编号和竞彩时间查询比赛
     */
    public static void queryByNumAndJCTime(String num, String jcTime) {
        System.out.println("=== 根据场次编号和竞彩时间查询 ===");
        System.out.println("场次编号: " + num);
        System.out.println("竞彩时间: " + jcTime);
        System.out.println();
        
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCTableSQLStatements.getSelectJCMatchByNumAndJCTimeSQL());
            statement.setString(0, num);
            statement.setString(1, jcTime);
            
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                printMatchInfo(resultSet);
            } else {
                System.out.println("未找到符合条件的比赛");
            }
            
        } catch (SQLException e) {
            System.err.println("查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        System.out.println();
    }
    
    /**
     * 根据竞彩日期查询比赛
     */
    public static void queryByJCDate(String jcDate) {
        System.out.println("=== 根据竞彩日期查询：" + jcDate + " ===");
        
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCTableSQLStatements.getSelectJCMatchesByJCDateSQL());
            statement.setString(1, jcDate);
            
            resultSet = statement.executeQuery();
            
            int count = 0;
            while (resultSet.next()) {
                if (count == 0) {
                    System.out.printf("%-10s %-20s %-10s %-15s %-15s%n", 
                                     "场次", "竞彩时间", "联赛", "主队", "客队");
                    System.out.println("=".repeat(80));
                }
                
                System.out.printf("%-10s %-20s %-10s %-15s %-15s%n",
                    resultSet.getString("num"),
                    resultSet.getString("jc_time"),
                    resultSet.getString("league"),
                    resultSet.getString("home"),
                    resultSet.getString("away")
                );
                count++;
            }
            
            if (count == 0) {
                System.out.println("未找到 " + jcDate + " 当天的比赛");
            } else {
                System.out.println("共找到 " + count + " 场比赛");
            }
            
        } catch (SQLException e) {
            System.err.println("查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        System.out.println();
    }
    
    /**
     * 根据竞彩时间范围查询比赛
     */
    public static void queryByJCTimeRange(String startTime, String endTime) {
        System.out.println("=== 根据竞彩时间范围查询 ===");
        System.out.println("开始时间: " + startTime);
        System.out.println("结束时间: " + endTime);
        System.out.println();
        
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCTableSQLStatements.getSelectJCMatchesByJCTimeRangeSQL());
            statement.setString(1, startTime);
            statement.setString(2, endTime);
            
            resultSet = statement.executeQuery();
            
            int count = 0;
            while (resultSet.next()) {
                if (count == 0) {
                    System.out.printf("%-10s %-20s %-10s %-15s %-15s%n", 
                                     "场次", "竞彩时间", "联赛", "主队", "客队");
                    System.out.println("=".repeat(80));
                }
                
                System.out.printf("%-10s %-20s %-10s %-15s %-15s%n",
                    resultSet.getString("num"),
                    resultSet.getString("jc_time"),
                    resultSet.getString("league"),
                    resultSet.getString("home"),
                    resultSet.getString("away")
                );
                count++;
            }
            
            if (count == 0) {
                System.out.println("未找到指定时间范围内的比赛");
            } else {
                System.out.println("共找到 " + count + " 场比赛");
            }
            
        } catch (SQLException e) {
            System.err.println("查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        System.out.println();
    }
    
    /**
     * 统计指定竞彩日期的比赛数量
     */
    public static int countByJCDate(String jcDate) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCTableSQLStatements.getCountJCMatchesByJCDateSQL());
            statement.setString(1, jcDate);
            
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                return resultSet.getInt("count");
            }
            
        } catch (SQLException e) {
            System.err.println("统计查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        return 0;
    }
    
    /**
     * 打印比赛信息
     */
    private static void printMatchInfo(ResultSet resultSet) throws SQLException {
        System.out.println("场次编号: " + resultSet.getString("num"));
        System.out.println("竞彩时间: " + resultSet.getString("jc_time"));
        System.out.println("比赛时间: " + resultSet.getString("match_time"));
        System.out.println("联赛: " + resultSet.getString("league"));
        System.out.println("主队: " + resultSet.getString("home"));
        System.out.println("客队: " + resultSet.getString("away"));
        System.out.println("半场比分: " + resultSet.getString("half_score"));
        System.out.println("全场比分: " + resultSet.getString("full_score"));
    }
}
