package com.tianyi.dao;

import com.tianyi.model.MatchData;
import com.tianyi.sql.JCTableSQLStatements;
import com.tianyi.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;

/**
 * 竞彩足球数据访问对象
 * 用于操作jcTable表的CRUD操作
 */
public class JCTableDAO {
    
    /**
     * 插入竞彩足球比赛数据（包含竞彩时间字段）
     */
    public boolean insertMatch(String num, String jcTime, String matchTime, String league,
                              String home, String away, String halfScore, String fullScore,
                              String winDrawLoseResult, String winDrawLoseOdds,
                              String handicap, String handicapResult, String handicapOdds,
                              String scoreResult, String scoreOdds,
                              String goalResult, String goalOdds,
                              String halfFullResult, String halfFullOdds) {

        String sql = JCTableSQLStatements.getInsertJCTableSQL();

        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(sql);

            statement.setString(1, num);
            statement.setString(2, jcTime);
            statement.setString(3, matchTime);
            statement.setString(4, league);
            statement.setString(5, home);
            statement.setString(6, away);
            statement.setString(7, halfScore);
            statement.setString(8, fullScore);
            statement.setString(9, winDrawLoseResult);
            statement.setString(10, winDrawLoseOdds);
            statement.setString(11, handicap);
            statement.setString(12, handicapResult);
            statement.setString(13, handicapOdds);
            statement.setString(14, scoreResult);
            statement.setString(15, scoreOdds);
            statement.setString(16, goalResult);
            statement.setString(17, goalOdds);
            statement.setString(18, halfFullResult);
            statement.setString(19, halfFullOdds);

            int result = statement.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("插入比赛数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
    
    /**
     * 查询所有比赛数据
     */
    public void getAllMatches() {
        String sql = "SELECT * FROM jcTable ORDER BY match_time ASC";
        
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            
            System.out.println("=== 所有比赛数据 ===");
            System.out.printf("%-10s %-15s %-15s %-10s %-15s %-15s %-10s %-10s%n",
                            "场次", "日期", "时间", "联赛", "主队", "客队", "半场", "全场");
            System.out.println("=".repeat(120));
            
            while (resultSet.next()) {
                System.out.printf("%-10s %-15s %-15s %-10s %-15s %-15s %-10s %-10s%n",
                    resultSet.getString("num"),
                        resultSet.getString("jc_time"),
                    resultSet.getString("match_time"),
                    resultSet.getString("league"),
                    resultSet.getString("home"),
                    resultSet.getString("away"),
                    resultSet.getString("half_score"),
                    resultSet.getString("full_score")
                );
            }
            
        } catch (SQLException e) {
            System.err.println("查询所有比赛数据失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }
    public void getMatchesByDateRange(String startDate, String endDate) {
        String sql = "SELECT * FROM jcTable WHERE jc_time BETWEEN ? AND ?";
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(sql);
            statement.setString(1, startDate);
            statement.setString(2, endDate);
            resultSet = statement.executeQuery();

            System.out.println("=== " + startDate + " 至 " + endDate + " 之间的比赛数据 ===");
            System.out.printf("%-10s %-15s %-15s %-10s %-15s %-15s %-10s %-10s%n",
                    "场次", "日期", "时间", "联赛", "主队", "客队", "半场", "全场");
            System.out.println("=".repeat(120));

            while (resultSet.next()) {
                System.out.printf("%-10s %-15s %-15s %-10s %-15s %-15s %-10s %-10s%n",
                        resultSet.getString("num"),
                        resultSet.getString("jc_time"),
                        resultSet.getString("match_time"),
                        resultSet.getString("league"),
                        resultSet.getString("home"),
                        resultSet.getString("away"),
                        resultSet.getString("half_score"),
                        resultSet.getString("full_score")
                );
            }
        } catch (SQLException e) {
            System.err.println("查询日期范围内的比赛数据失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }
    
    /**
     * 根据联赛查询比赛数据
     */
    public void getMatchesByLeague(String league) {
        String sql = "SELECT * FROM jcTable WHERE league = ? ORDER BY match_time DESC";
        
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(sql);
            statement.setString(1, league);
            
            resultSet = statement.executeQuery();
            
            System.out.println("=== " + league + " 联赛比赛数据 ===");
            System.out.printf("%-10s %-15s %-15s %-15s %-10s %-10s%n", 
                            "场次", "时间", "主队", "客队", "半场", "全场");
            System.out.println("=".repeat(80));
            
            while (resultSet.next()) {
                System.out.printf("%-10s %-15s %-15s %-15s %-10s %-10s%n",
                    resultSet.getString("num"),
                    resultSet.getString("match_time"),
                    resultSet.getString("home"),
                    resultSet.getString("away"),
                    resultSet.getString("half_score"),
                    resultSet.getString("full_score")
                );
            }
            
        } catch (SQLException e) {
            System.err.println("查询联赛比赛数据失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }
    
    /**
     * 统计表中的记录数
     */
    public int getMatchCount() {
        String sql = "SELECT COUNT(*) as count FROM jcTable";
        
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            
            if (resultSet.next()) {
                return resultSet.getInt("count");
            }
            
        } catch (SQLException e) {
            System.err.println("统计记录数失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        return 0;
    }

    /**
     * 根据联赛查询比赛数据
     */
    public ArrayList<MatchData> getMatchesByDate(String date) {
        String sql = "SELECT * FROM jcTable WHERE jc_time = ? ORDER BY num ASC";

        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        ArrayList<MatchData> matchList = new ArrayList<>();

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(sql);
            statement.setString(1, date);

            resultSet = statement.executeQuery();

            // 遍历结果集，创建MatchData对象并添加到列表中
            while (resultSet.next()) {
                MatchData matchData = new MatchData();
                // 设置MatchData的各个属性
                matchData.setNum(resultSet.getString("num"));
                matchData.setMatchTime(resultSet.getString("match_time"));
                matchData.setLeague(resultSet.getString("league"));
                matchData.setHome(resultSet.getString("home"));
                matchData.setAway(resultSet.getString("away"));

                // 创建并设置MatchResult
                MatchData.MatchResult result = new MatchData.MatchResult();
                result.setHalf(resultSet.getString("half_score"));
                result.setFull(resultSet.getString("full_score"));
                result.setWinDrawLoseResult(resultSet.getString("win_draw_lose_result"));
                result.setWinDrawLoseOdds(resultSet.getString("win_draw_lose_odds"));
                result.setHandicap(resultSet.getString("handicap"));
                result.setHandicapResult(resultSet.getString("handicap_result"));
                result.setHandicapOdds(resultSet.getString("handicap_odds"));
                result.setScoreResult(resultSet.getString("score_result"));
                result.setScoreOdds(resultSet.getString("score_odds"));
                result.setGoalResult(resultSet.getString("goal_result"));
                result.setGoalOdds(resultSet.getString("goal_odds"));
                result.setHalfFullResult(resultSet.getString("half_full_result"));
                result.setHalfFullOdds(resultSet.getString("half_full_odds"));

                matchData.setResult(result);
                matchList.add(matchData); // 添加到列表中
            }

        } catch (SQLException e) {
            System.err.println("查询某一天的比赛数据失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }

        return matchList;
    }
}
