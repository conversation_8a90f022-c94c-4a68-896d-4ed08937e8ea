package com.tianyi.database;

import com.tianyi.util.DatabaseUtil;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库管理类
 * 负责数据库的创建、初始化和基本表结构的创建
 */
public class DatabaseManager {
    
    /**
     * 初始化数据库
     * 创建数据库并建立基本表结构
     */
    public static boolean initializeDatabase() {
        System.out.println("开始初始化TianyiSport数据库...");
        
        // 1. 创建数据库
        if (!DatabaseUtil.createDatabase()) {
            System.err.println("数据库创建失败！");
            return false;
        }
        
        // 2. 测试连接
        if (!DatabaseUtil.testConnection()) {
            System.err.println("数据库连接测试失败！");
            return false;
        }
        
        System.out.println("TianyiSport数据库初始化完成！");
        return true;
    }

    /**
     * 检查数据库是否存在
     */
    public static boolean databaseExists() {
        try {
            Connection connection = DatabaseUtil.getConnection();
            connection.close();
            return true;
        } catch (SQLException e) {
            return false;
        }
    }
}
