package com.tianyi.database;

import com.tianyi.sql.JCEndResultSQLStatements;
import com.tianyi.sql.JCFEResultSQLStatements;
import com.tianyi.sql.JCFSResultSQLStatements;
import com.tianyi.sql.JCTableSQLStatements;
import com.tianyi.util.DatabaseUtil;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库管理类
 * 负责数据库的创建、初始化和基本表结构的创建
 */
public class DatabaseManager {
    
    /**
     * 初始化数据库
     * 创建数据库并建立基本表结构
     */
    public static boolean initializeDatabase() {
        System.out.println("开始初始化TianyiSport数据库...");
        
        // 1. 创建数据库
        if (!DatabaseUtil.createDatabase()) {
            System.err.println("数据库创建失败！");
            return false;
        }
        
        // 2. 测试连接
        if (!DatabaseUtil.testConnection()) {
            System.err.println("数据库连接测试失败！");
            return false;
        }
        
        System.out.println("TianyiSport数据库初始化完成！");
        return true;
    }
    
    /**
     * 创建基本表结构
     */
    public static void createBasicTables() {
        Connection connection = null;
        Statement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();

            // 使用SQL语句处理类创建竞彩足球表
//            String createJCTable = JCTableSQLStatements.getCreateJCTableSQL();
//            String createFSTable = JCFSResultSQLStatements.getCreateFSTableSQL();
//            String createFETable = JCFEResultSQLStatements.getCreateFETableSQL();
//            String createEndTable = JCEndResultSQLStatements.getCreateEndTableSQL();

//            statement.executeUpdate(createJCTable);
//            statement.executeUpdate(createFSTable);
//            statement.executeUpdate(createFETable);
//            statement.executeUpdate(createEndTable);
//            System.out.println("竞彩足球表(jcTable)创建成功");

            return;

        } catch (SQLException e) {
            System.err.println("创建表结构失败: " + e.getMessage());
            return;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
    
    /**
     * 检查数据库是否存在
     */
    public static boolean databaseExists() {
        try {
            Connection connection = DatabaseUtil.getConnection();
            connection.close();
            return true;
        } catch (SQLException e) {
            return false;
        }
    }
}
