package com.tianyi.model;

import java.util.HashMap;
import java.util.Map;

/**
 * 竞彩结束场次购买结果数据类
 * 对应jcEndBuyResultTable表结构
 */
public class JCBuyResultModel {
    private String jcDate;
    private String num;
    private String result;

    private String odds;

    private String teamAndOdds;

    private String type;

    private int fsMatch;

    private int feMatch;

    private int lastMatch;

    private int matchNum;

    private String printMessage;

    public String getJcDate() {
        return jcDate;
    }

    public void setJcDate(String jcDate) {
        this.jcDate = jcDate;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getOdds() { return odds; }

    public void setOdds(String odds) { this.odds = odds; }

    public String getTeamAndOdds() { return teamAndOdds; }

    public void setTeamAndOdds(String teamAndOdds) { this.teamAndOdds = teamAndOdds; }

    public String getType() { return type; }

    public void setType(String type) { this.type = type; }

    public int getFsMatch() {
        return fsMatch;
    }

    public void setFsMatch(int fsMatch) {
        this.fsMatch = fsMatch;
    }

    public int getFeMatch() {
        return feMatch;
    }

    public void setFeMatch(int feMatch) {
        this.feMatch = feMatch;
    }

    public int getLastMatch() {
        return lastMatch;
    }

    public void setLastMatch(int lastMatch) {
        this.lastMatch = lastMatch;
    }

    public int getMatchNum() {
        return matchNum;
    }

    public void setMatchNum(int matchNum) {
        this.matchNum = matchNum;
    }

    public void setPrintMessage(String printMessage) {
        this.printMessage = printMessage;
    }

    public String getPrintMessage() {
        return printMessage;
    }
}




