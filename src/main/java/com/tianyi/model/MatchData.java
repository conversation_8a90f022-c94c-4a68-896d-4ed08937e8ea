package com.tianyi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tianyi.sql.JCTableSQLStatements;
import com.tianyi.util.DatabaseUtil;
import com.tianyi.util.JCDataUtil;
import com.tianyi.util.TimeUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 比赛数据模型类
 * 用于映射JSON文件中的比赛数据结构
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MatchData {
    
    @JsonProperty("num")
    private String num;
    
    @JsonProperty("match_time")
    private String matchTime;
    
    @JsonProperty("league")
    private String league;
    
    @JsonProperty("home")
    private String home;
    
    @JsonProperty("away")
    private String away;
    
    @JsonProperty("result")
    private MatchResult result;
    
    // 构造函数
    public MatchData() {}
    
    // Getter和Setter方法
    public String getNum() {
        return num;
    }
    
    public void setNum(String num) {
        this.num = num;
    }
    
    public String getMatchTime() {
        return matchTime;
    }
    
    public void setMatchTime(String matchTime) {
        this.matchTime = matchTime;
    }
    
    public String getLeague() {
        return league;
    }
    
    public void setLeague(String league) {
        this.league = league;
    }
    
    public String getHome() {
        return home;
    }
    
    public void setHome(String home) {
        this.home = home;
    }
    
    public String getAway() {
        return away;
    }
    
    public void setAway(String away) {
        this.away = away;
    }
    
    public MatchResult getResult() {
        return result;
    }
    
    public void setResult(MatchResult result) {
        this.result = result;
    }
    
    @Override
    public String toString() {
        return String.format(
            "比赛信息: %s | %s | %s vs %s | 比赛时间: %s | ID: %s",
            num, league, home, away, matchTime
        );
    }

    /**
     * 打印详细信息（包含场次日期和转换后的时间）
     */
    public void insertDetailedInfo(String timeStr) {
        System.out.println("=== 插入比赛信息 ===");
        System.out.println("场次编号: " + num);

        String jcDate = "";
        String jcTime = "";
        // 添加场次日期信息
        try {
            jcDate = TimeUtil.getJCDateFromWeekday(timeStr, matchTime, num);
        } catch (Exception e) {
            System.out.println("场次日期: 无法转换 (" + e.getMessage() + ")");
        }

        // 添加转换后的具体时间
        try {
            jcTime = TimeUtil.getJCTimeFromWeekday(timeStr, matchTime);
        } catch (Exception e) {
            System.out.println("竞彩时间: 无法转换 (" + e.getMessage() + ")");
        }

        insertMatchData(num, jcDate, jcTime);
    }

    public void insertMatchData(String num, String jcDate, String jcTime) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCTableSQLStatements.getSelectJCMatchByNumAndJCTimeSQL());
            statement.setString(1, num);
            statement.setString(2, jcDate);

            resultSet = statement.executeQuery();

            if (!resultSet.next()) {
                // 表中不存在该数据,应该插入到表中
                JCDataUtil.insertMatchData(
                        num,
                        jcDate,
                        jcTime,
                        league,
                        home,
                        away,
                        result.getHalf(),
                        result.getFull(),
                        result.getWinDrawLoseResult(),
                        result.getWinDrawLoseOdds(),
                        result.getHandicap(),
                        result.getHandicapResult(),
                        result.getHandicapOdds(),
                        result.getScoreResult(),
                        result.getScoreOdds(),
                        result.getGoalResult(),
                        result.getGoalOdds(),
                        result.getHalfFullResult(),
                        result.getHalfFullOdds());
            } else {
                System.out.println("已存在该数据:");
                System.out.println("当前日期:"+jcDate);
                System.out.println(resultSet.getString("num"));
                System.out.println(resultSet.getString("jc_time"));
                System.out.println(resultSet.getString("match_time"));
                System.out.println(resultSet.getString("home"));
                System.out.println();
            }
            Thread.sleep(80); // 延时0.08秒
        } catch (SQLException e) {
            System.err.println("查询失败: " + e.getMessage());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
    }
    
    /**
     * 内部类：比赛结果
     */
    public static class MatchResult {
        
        @JsonProperty("half")
        private String half;
        
        @JsonProperty("full")
        private String full;
        
        @JsonProperty("win_draw_lose_result")
        private String winDrawLoseResult;
        
        @JsonProperty("win_draw_lose_odds")
        private String winDrawLoseOdds;
        
        @JsonProperty("handicap")
        private String handicap;
        
        @JsonProperty("handicap_result")
        private String handicapResult;
        
        @JsonProperty("handicap_odds")
        private String handicapOdds;
        
        @JsonProperty("score_result")
        private String scoreResult;
        
        @JsonProperty("score_odds")
        private String scoreOdds;
        
        @JsonProperty("goal_result")
        private String goalResult;
        
        @JsonProperty("goal_odds")
        private String goalOdds;
        
        @JsonProperty("half_full_result")
        private String halfFullResult;
        
        @JsonProperty("half_full_odds")
        private String halfFullOdds;
        
        // 构造函数
        public MatchResult() {}
        
        // Getter和Setter方法
        public String getHalf() { return half; }
        
        public String getFull() { return full; }
        
        public String getWinDrawLoseResult() { return winDrawLoseResult; }
        
        public String getWinDrawLoseOdds() { return winDrawLoseOdds; }
        
        public String getHandicap() { return handicap; }
        
        public String getHandicapResult() { return handicapResult; }
        
        public String getHandicapOdds() { return handicapOdds; }
        
        public String getScoreResult() { return scoreResult; }
        
        public String getScoreOdds() { return scoreOdds; }
        
        public String getGoalResult() { return goalResult; }
        
        public String getGoalOdds() { return goalOdds; }
        
        public String getHalfFullResult() { return halfFullResult; }
        
        public String getHalfFullOdds() { return halfFullOdds; }
    }
}
