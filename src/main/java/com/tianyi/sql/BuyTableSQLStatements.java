package com.tianyi.sql;

/**
 * BuyTable SQL语句处理类
 * 用于存储购买记录表相关的SQL语句
 */
public class BuyTableSQLStatements {
    
    /**
     * 创建购买记录表的SQL语句
     */
    public static final String CREATE_BUY_TABLE = """
        CREATE TABLE IF NOT EXISTS buyTable (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
            match_id VARCHAR(20) NOT NULL COMMENT '比赛ID',
            num VARCHAR(20) NOT NULL COMMENT '场次编号',
            buy_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
            jc_time VARCHAR(20) COMMENT '竞彩时间',
            match_time VARCHAR(20) COMMENT '比赛时间',
            league VARCHAR(100) COMMENT '联赛名称',
            home VARCHAR(100) COMMENT '主队名称',
            away VARCHAR(100) COMMENT '客队名称',
            
            -- 购买信息
            bet_type VARCHAR(20) NOT NULL COMMENT '投注类型(胜平负/让球/比分/总进球/半全场)',
            bet_option VARCHAR(50) NOT NULL COMMENT '投注选项',
            bet_odds VARCHAR(10) COMMENT '投注赔率',
            bet_amount DECIMAL(10,2) NOT NULL COMMENT '投注金额',
            potential_return DECIMAL(10,2) COMMENT '预期收益',
            
            -- 结果信息
            match_result VARCHAR(10) COMMENT '比赛结果',
            bet_result VARCHAR(10) COMMENT '投注结果(中奖/未中奖)',
            actual_return DECIMAL(10,2) DEFAULT 0.00 COMMENT '实际收益',
            profit_loss DECIMAL(10,2) DEFAULT 0.00 COMMENT '盈亏金额',
            
            -- 状态信息
            status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态(PENDING/WON/LOST/CANCELLED)',
            notes TEXT COMMENT '备注',
            
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_user_id (user_id),
            INDEX idx_match_id (match_id),
            INDEX idx_num (num),
            INDEX idx_buy_time (buy_time),
            INDEX idx_bet_type (bet_type),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买记录表'
        """;
    
    /**
     * 插入购买记录的SQL语句
     */
    public static final String INSERT_BUY_RECORD = """
        INSERT INTO buyTable (
            user_id, match_id, num, jc_time, match_time, league, home, away,
            bet_type, bet_option, bet_odds, bet_amount, potential_return,
            notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
    
    /**
     * 查询所有购买记录的SQL语句
     */
    public static final String SELECT_ALL_BUY_RECORDS = """
        SELECT * FROM buyTable ORDER BY buy_time DESC
        """;
    
    /**
     * 根据用户ID查询购买记录的SQL语句
     */
    public static final String SELECT_BUY_RECORDS_BY_USER_ID = """
        SELECT * FROM buyTable WHERE user_id = ? ORDER BY buy_time DESC
        """;
    
    /**
     * 根据比赛ID查询购买记录的SQL语句
     */
    public static final String SELECT_BUY_RECORDS_BY_MATCH_ID = """
        SELECT * FROM buyTable WHERE match_id = ? ORDER BY buy_time DESC
        """;
    
    /**
     * 根据场次编号查询购买记录的SQL语句
     */
    public static final String SELECT_BUY_RECORDS_BY_NUM = """
        SELECT * FROM buyTable WHERE num = ? ORDER BY buy_time DESC
        """;
    
    /**
     * 根据投注类型查询购买记录的SQL语句
     */
    public static final String SELECT_BUY_RECORDS_BY_BET_TYPE = """
        SELECT * FROM buyTable WHERE bet_type = ? ORDER BY buy_time DESC
        """;
    
    /**
     * 根据状态查询购买记录的SQL语句
     */
    public static final String SELECT_BUY_RECORDS_BY_STATUS = """
        SELECT * FROM buyTable WHERE status = ? ORDER BY buy_time DESC
        """;
    
    /**
     * 根据购买时间范围查询购买记录的SQL语句
     */
    public static final String SELECT_BUY_RECORDS_BY_TIME_RANGE = """
        SELECT * FROM buyTable 
        WHERE buy_time BETWEEN ? AND ? 
        ORDER BY buy_time DESC
        """;
    
    /**
     * 更新购买记录结果的SQL语句
     */
    public static final String UPDATE_BUY_RECORD_RESULT = """
        UPDATE buyTable SET 
            match_result = ?, bet_result = ?, actual_return = ?, 
            profit_loss = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """;
    
    /**
     * 删除购买记录的SQL语句
     */
    public static final String DELETE_BUY_RECORD = """
        DELETE FROM buyTable WHERE id = ?
        """;
    
    /**
     * 统计购买记录总数的SQL语句
     */
    public static final String COUNT_BUY_RECORDS = """
        SELECT COUNT(*) as count FROM buyTable
        """;
    
    /**
     * 统计用户购买记录数的SQL语句
     */
    public static final String COUNT_BUY_RECORDS_BY_USER = """
        SELECT COUNT(*) as count FROM buyTable WHERE user_id = ?
        """;
    
    /**
     * 统计用户总投注金额的SQL语句
     */
    public static final String SUM_BET_AMOUNT_BY_USER = """
        SELECT SUM(bet_amount) as total_amount FROM buyTable WHERE user_id = ?
        """;
    
    /**
     * 统计用户总盈亏的SQL语句
     */
    public static final String SUM_PROFIT_LOSS_BY_USER = """
        SELECT SUM(profit_loss) as total_profit_loss FROM buyTable WHERE user_id = ?
        """;
    
    /**
     * 获取SQL语句的方法
     */
    public static String getCreateBuyTableSQL() {
        return CREATE_BUY_TABLE;
    }
    
    public static String getInsertBuyRecordSQL() {
        return INSERT_BUY_RECORD;
    }
    
    public static String getSelectAllBuyRecordsSQL() {
        return SELECT_ALL_BUY_RECORDS;
    }
    
    public static String getSelectBuyRecordsByUserIdSQL() {
        return SELECT_BUY_RECORDS_BY_USER_ID;
    }
    
    public static String getSelectBuyRecordsByMatchIdSQL() {
        return SELECT_BUY_RECORDS_BY_MATCH_ID;
    }
    
    public static String getSelectBuyRecordsByNumSQL() {
        return SELECT_BUY_RECORDS_BY_NUM;
    }
    
    public static String getSelectBuyRecordsByBetTypeSQL() {
        return SELECT_BUY_RECORDS_BY_BET_TYPE;
    }
    
    public static String getSelectBuyRecordsByStatusSQL() {
        return SELECT_BUY_RECORDS_BY_STATUS;
    }
    
    public static String getSelectBuyRecordsByTimeRangeSQL() {
        return SELECT_BUY_RECORDS_BY_TIME_RANGE;
    }
    
    public static String getUpdateBuyRecordResultSQL() {
        return UPDATE_BUY_RECORD_RESULT;
    }
    
    public static String getDeleteBuyRecordSQL() {
        return DELETE_BUY_RECORD;
    }
    
    public static String getCountBuyRecordsSQL() {
        return COUNT_BUY_RECORDS;
    }
    
    public static String getCountBuyRecordsByUserSQL() {
        return COUNT_BUY_RECORDS_BY_USER;
    }
    
    public static String getSumBetAmountByUserSQL() {
        return SUM_BET_AMOUNT_BY_USER;
    }
    
    public static String getSumProfitLossByUserSQL() {
        return SUM_PROFIT_LOSS_BY_USER;
    }
}
