package com.tianyi.sql;

public class JCBuyResultTableSQLStatements {

    public static final String CREATE_HALF_FULL_BUY_RESULT_TABLE = """
                CREATE TABLE IF NOT EXISTS halfFullBuyResultTable (
                    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    date_time VARCHAR(20) NOT NULL COMMENT '比赛日期',
                    num VARCHAR(50) NOT NULL COMMENT '场次编号(1_2)',
                    result VARCHAR(20) NOT NULL COMMENT '结果',
                    odds VARCHAR(10) NOT NULL COMMENT '赔率',
                    teamAndOdds TEXT NOT NULL COMMENT '球队+胜平负赔率(阿森纳-曼城(2.8:3.4:2.1)_曼联-曼城(2.8:3.4:2.1))',
                    type VARCHAR(10) NOT NULL COMMENT '类型:0-正常,1-强(主要是看主队)',
                    lastMatch INT NOT NULL COMMENT '最后两场:0-否,1-是',
                    feMatch INT NOT NULL COMMENT '首末两场:0-否,1-是',
                    fsMatch INT NOT NULL COMMENT '首位两场:0-否,1-是',
                    matchNum INT NOT NULL COMMENT '场次数目(最大4，最小2)',
                
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                    INDEX idx_date_time (date_time),
                    INDEX idx_result (result),
                    INDEX idx_num (num),
                    INDEX idx_type (type),
                    INDEX idx_matchNum (matchNum)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩半全场模拟场次购买数据表'
                """;

    public static final String CREATE_HANDICAP_BUY_RESULT_TABLE = """
                CREATE TABLE IF NOT EXISTS handicapBuyResultTable (
                    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    date_time VARCHAR(20) NOT NULL COMMENT '比赛日期',
                    num VARCHAR(50) NOT NULL COMMENT '场次编号(1_2)',
                    result VARCHAR(20) NOT NULL COMMENT '结果',
                    odds VARCHAR(10) NOT NULL COMMENT '赔率',
                    teamAndOdds TEXT NOT NULL COMMENT '球队+胜平负赔率(阿森纳-曼城(2.8:3.4:2.1)_曼联-曼城(2.8:3.4:2.1))',
                    type VARCHAR(10) NOT NULL COMMENT '类型:0-正常,1-强(主要是看主队)',
                    lastMatch INT NOT NULL COMMENT '最后两场:0-否,1-是',
                    feMatch INT NOT NULL COMMENT '首末两场:0-否,1-是',
                    fsMatch INT NOT NULL COMMENT '首位两场:0-否,1-是',
                    matchNum INT NOT NULL COMMENT '场次数目(最大4，最小2)',
                
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                    INDEX idx_date_time (date_time),
                    INDEX idx_result (result),
                    INDEX idx_num (num),
                    INDEX idx_type (type),
                    INDEX idx_matchNum (matchNum)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩让球模拟场次购买数据表'
                """;

    public static final String CREATE_GOAL_BUY_RESULT_TABLE = """
                CREATE TABLE IF NOT EXISTS goalBuyResultTable (
                    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    date_time VARCHAR(20) NOT NULL COMMENT '比赛日期',
                    num VARCHAR(50) NOT NULL COMMENT '场次编号(1_2)',
                    result VARCHAR(20) NOT NULL COMMENT '结果',
                    odds VARCHAR(10) NOT NULL COMMENT '赔率',
                    teamAndOdds TEXT NOT NULL COMMENT '球队+胜平负赔率(阿森纳-曼城(2.8:3.4:2.1)_曼联-曼城(2.8:3.4:2.1))',
                    lastMatch INT NOT NULL COMMENT '最后两场:0-否,1-是',
                    feMatch INT NOT NULL COMMENT '首末两场:0-否,1-是',
                    fsMatch INT NOT NULL COMMENT '首位两场:0-否,1-是',
                    matchNum INT NOT NULL COMMENT '场次数目(最大4，最小2)',
                
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                    INDEX idx_date_time (date_time),
                    INDEX idx_result (result),
                    INDEX idx_num (num),
                    INDEX idx_matchNum (matchNum)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩进球数模拟场次购买数据表'
                """;

    public static final String CREATE_SCORE_BUY_RESULT_TABLE = """
                CREATE TABLE IF NOT EXISTS scoreBuyResultTable (
                    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    date_time VARCHAR(20) NOT NULL COMMENT '比赛日期',
                    num VARCHAR(50) NOT NULL COMMENT '场次编号(1_2)',
                    result VARCHAR(20) NOT NULL COMMENT '结果',
                    odds VARCHAR(10) NOT NULL COMMENT '赔率',
                    teamAndOdds TEXT NOT NULL COMMENT '球队+胜平负赔率(阿森纳-曼城(2.8:3.4:2.1)_曼联-曼城(2.8:3.4:2.1))',
                    lastMatch INT NOT NULL COMMENT '最后两场:0-否,1-是',
                    feMatch INT NOT NULL COMMENT '首末两场:0-否,1-是',
                    fsMatch INT NOT NULL COMMENT '首位两场:0-否,1-是',
                    matchNum INT NOT NULL COMMENT '场次数目(最大4，最小2)',
                
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                    INDEX idx_date_time (date_time),
                    INDEX idx_result (result),
                    INDEX idx_num (num),
                    INDEX idx_matchNum (matchNum)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩比分模拟场次购买数据表'
                """;

    public static final String INSERT_JC_HALF_FULL_BUY_RESULT_TABLE = """
            INSERT INTO halfFullBuyResultTable (
                date_time,
                num,
                result,
                odds,
                teamAndOdds,
                type,
                lastMatch,
                feMatch,
                fsMatch,
                matchNum
            ) VALUES (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
            )
    """;

    public static final String INSERT_JC_HANDICAP_BUY_RESULT_TABLE = """
            INSERT INTO handicapBuyResultTable (
                        date_time,
                        num,
                        result,
                        odds,
                        teamAndOdds,
                        type,
                        lastMatch,
                        feMatch,
                        fsMatch,
                        matchNum
                    ) VALUES (
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?
                    );
    """;

    public static final String INSERT_JC_GOAL_BUY_RESULT_TABLE = """
            INSERT INTO goalBuyResultTable (
                        date_time,
                        num,
                        result,
                        odds,
                        teamAndOdds,
                        lastMatch,
                        feMatch,
                        fsMatch,
                        matchNum
                    ) VALUES (
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?
                    );
    """;

    public static final String INSERT_JC_SCORE_BUY_RESULT_TABLE = """
            INSERT INTO scoreBuyResultTable (
                        date_time,
                        num,
                        result,
                        odds,
                        teamAndOdds,
                        lastMatch,
                        feMatch,
                        fsMatch,
                        matchNum
                    ) VALUES (
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?
                    );
    """;

    public static final String SELECT_JC_HALF_FULL_BY_DATE = """
        SELECT * FROM halfFullBuyResultTable
        WHERE date_time = ?
        """;

    public static final String SELECT_JC_HALF_FULL_BY_DATE_AND_NUM = """
        SELECT * FROM halfFullBuyResultTable
        WHERE date_time = ? AND num = ?
        """;

    public static final String SELECT_JC_HALF_FULL_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM halfFullBuyResultTable
        WHERE date_time = ? AND num = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_HALF_FULL_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM halfFullBuyResultTable
        WHERE date_time = ? AND matchNum = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_HANDICAP_BY_DATE_AND_NUM = """
        SELECT * FROM handicapBuyResultTable
        WHERE date_time = ? AND num = ?
        """;

    public static final String SELECT_JC_HANDICAP_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM handicapBuyResultTable
        WHERE date_time = ? AND num = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_HANDICAP_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM handicapBuyResultTable
        WHERE date_time = ? AND matchNum = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;


    public static final String SELECT_JC_SCORE_BY_DATE_AND_NUM = """
        SELECT * FROM scoreBuyResultTable
        WHERE date_time = ? AND num = ?
        """;

    public static final String SELECT_JC_SCORE_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM scoreBuyResultTable
        WHERE date_time = ? AND num = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_SCORE_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM scoreBuyResultTable
        WHERE date_time = ? AND matchNum = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_GOAL_BY_DATE_AND_NUM = """
        SELECT * FROM goalBuyResultTable
        WHERE date_time = ? AND num = ?
        """;

    public static final String SELECT_JC_GOAL_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM goalBuyResultTable
        WHERE date_time = ? AND num = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_GOAL_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END = """
        SELECT * FROM goalBuyResultTable
        WHERE date_time = ? AND matchNum = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_HALF_FULL_BY_DATE_AND_NUM_AND_RESULT_AND_TYPE = """
        SELECT * FROM halfFullBuyResultTable
        WHERE date_time = ? AND matchNum = ? AND result = ? AND type = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_HANDICAP_BY_DATE_AND_NUM_AND_RESULT_AND_TYPE = """
        SELECT * FROM handicapBuyResultTable
        WHERE date_time = ? AND matchNum = ? AND result = ? AND type = ? AND fsMatch = ? AND feMatch = ? AND lastMatch = ?
        """;

    public static final String SELECT_JC_GOAL_BY_DATE_AND_NUM_AND_RESULT = """
        SELECT * FROM goalBuyResultTable
        WHERE date_time = ? AND num = ? AND result = ?
        """;

    public static final String DROP_HALF_FULL_TABLE = """
        DROP TABLE IF EXISTS halfFullBuyResultTable
        """;

    public static final String DROP_HANDICAP_TABLE = """
        DROP TABLE IF EXISTS handicapBuyResultTable
        """;

    public static final String DROP_GOAL_TABLE = """
        DROP TABLE IF EXISTS goalBuyResultTable
        """;

    public static final String DROP_SCORE_TABLE = """
        DROP TABLE IF EXISTS scoreBuyResultTable
        """;

    public static String getCreateHalfFullTableSQL() {
        return CREATE_HALF_FULL_BUY_RESULT_TABLE;
    }

    public static String getCreateHandicapTableSQL() {
        return CREATE_HANDICAP_BUY_RESULT_TABLE;
    }

    public static String getCreateGoalTableSQL() {
        return CREATE_GOAL_BUY_RESULT_TABLE;
    }

    public static String getCreateScoreTableSQL() {
        return CREATE_SCORE_BUY_RESULT_TABLE;
    }

    public static String getDropHalfFullTableSQL() {
        return DROP_HALF_FULL_TABLE;
    }

    public static String getDropHandicapTableSQL() {
        return DROP_HANDICAP_TABLE;
    }

    public static String getDropGoalTableSQL() {
        return DROP_GOAL_TABLE;
    }

    public static String getDropScoreTableSQL() {
        return DROP_SCORE_TABLE;
    }

    public static String getInsertHalfFullTableSQL() {
        return INSERT_JC_HALF_FULL_BUY_RESULT_TABLE;
    }

    public static String getInsertHandicapTableSQL() {
        return INSERT_JC_HANDICAP_BUY_RESULT_TABLE;
    }

    public static String getInsertGoalTableSQL() {
        return INSERT_JC_GOAL_BUY_RESULT_TABLE;
    }

    public static String getInsertScoreTableSQL() {
        return INSERT_JC_SCORE_BUY_RESULT_TABLE;
    }

    public static String getSearchHalfFullTableByDateAndNumSQL() {
        return SELECT_JC_HALF_FULL_BY_DATE_AND_NUM;
    }

    public static String getSearchHalfFullTableByDateSQL() {
        return SELECT_JC_HALF_FULL_BY_DATE;
    }

    public static String getSearchHalfFullTableByDateAndNumAndFsFeEndSQL() {
        return SELECT_JC_HALF_FULL_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END;
    }

    public static String getSearchHalfFullTableByDateAndMatchNumAndFsFeEndSQL() {
        return SELECT_JC_HALF_FULL_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END;
    }

    public static String getSearchHandicapTableByDateAndNumSQL() {
        return SELECT_JC_HANDICAP_BY_DATE_AND_NUM;
    }

    public static String getSearchHandicapTableByDateAndNumAndFsFeEndSQL() {
        return SELECT_JC_HANDICAP_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END;
    }

    public static String getSearchHandicapTableByDateAndMatchNumAndFsFeEndSQL() { return SELECT_JC_HANDICAP_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END; }

    public static String getSearchScoreTableByDateAndNumSQL() {
        return SELECT_JC_SCORE_BY_DATE_AND_NUM;
    }

    public static String getSearchScoreTableByDateAndNumAndFsFeEndSQL() {
        return SELECT_JC_SCORE_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END;
    }

    public static String getSearchScoreTableByDateAndMatchNumAndFsFeEndSQL() { return SELECT_JC_SCORE_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END; }

    public static String getSearchGoalTableByDateAndNumSQL() { return SELECT_JC_GOAL_BY_DATE_AND_NUM; }

    public static String getSearchGoalTableByDateAndNumAndFsFeEndSQL() { return SELECT_JC_GOAL_BY_DATE_AND_NUM_AND_FS_AND_FE_AND_END; }

    public static String getSearchGoalTableByDateAndMatchNumAndFsFeEndSQL() { return SELECT_JC_GOAL_BY_DATE_AND_MATCH_NUM_AND_FS_AND_FE_AND_END; }

    public static String getSearchHalfFullTableByDateAndNumResultTypeSQL() {
        return SELECT_JC_HALF_FULL_BY_DATE_AND_NUM_AND_RESULT_AND_TYPE;
    }

    public static String getSearchHandicapTableByDateAndNumResultTypeSQL() {
        return SELECT_JC_HANDICAP_BY_DATE_AND_NUM_AND_RESULT_AND_TYPE;
    }

    public static String getSearchGoalTableByDateAndNumResultSQL() { return SELECT_JC_GOAL_BY_DATE_AND_NUM_AND_RESULT; }
}
