package com.tianyi.sql;

public class JCEndResultSQLStatements {
    /**
     * 创建竞彩固定场次购买结果表的SQL语句(每日最后两场比赛)
     */
    public static final String CREATE_JC_End_BUY_RESULT_TABLE = """
    CREATE TABLE IF NOT EXISTS jcBuyE12ResultTable (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        jc_time VARCHAR(20) NOT NULL COMMENT '竞彩日期',
        
        e1_num VARCHAR(20) COMMENT '倒数第二场关联的比赛场次编号',
        e2_num VARCHAR(20) COMMENT '倒数第一场关联的比赛场次编号',
        
        -- 胜平负 
        -- 胜 ~ 胜
        win_lose_cash_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_33 VARCHAR(10) COMMENT '赔率',
        -- 胜 ~ 平
        win_lose_cash_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_31 VARCHAR(10) COMMENT '赔率',
        -- 胜 ~ 负
        win_lose_cash_30 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_30 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 胜
        win_lose_cash_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_13 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 平
        win_lose_cash_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_11 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 负
        win_lose_cash_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_10 VARCHAR(10) COMMENT '赔率',
        -- 负 ~ 胜
        win_lose_cash_03 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_03 VARCHAR(10) COMMENT '赔率',
        -- 负 ~ 平
        win_lose_cash_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_01 VARCHAR(10) COMMENT '赔率',
        -- 负 ~ 负
        win_lose_cash_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        win_lose_odds_00 VARCHAR(10) COMMENT '赔率',
        
        -- 让球 
        -- 让胜 ~ 让胜
        handicap_cash_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_33 VARCHAR(10) COMMENT '赔率',
        -- 让胜 ~ 让平
        handicap_cash_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_31 VARCHAR(10) COMMENT '赔率',
        -- 让胜 ~ 让负
        handicap_cash_30 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_30 VARCHAR(10) COMMENT '赔率',
        -- 让平 ~ 让胜
        handicap_cash_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_13 VARCHAR(10) COMMENT '赔率',
        -- 让平 ~ 让平
        handicap_cash_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_11 VARCHAR(10) COMMENT '赔率',
        -- 让平 ~ 让负
        handicap_cash_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_10 VARCHAR(10) COMMENT '赔率',
        -- 让负 ~ 让胜
        handicap_cash_03 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_03 VARCHAR(10) COMMENT '赔率',
        -- 让负 ~ 让平
        handicap_cash_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_01 VARCHAR(10) COMMENT '赔率',
        -- 让负 ~ 让负
        handicap_cash_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_odds_00 VARCHAR(10) COMMENT '赔率',
        
        -- 半全场 
        -- 胜胜 ~ 胜胜
        half_full_cash_33_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_33 VARCHAR(10) COMMENT '赔率',
        -- 胜胜 ~ 胜平
        half_full_cash_33_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_31 VARCHAR(10) COMMENT '赔率',
        -- 胜胜 ~ 平胜
        half_full_cash_33_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_13 VARCHAR(10) COMMENT '赔率',
        -- 胜胜 ~ 平平
        half_full_cash_33_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_11 VARCHAR(10) COMMENT '赔率',
        -- 胜胜 ~ 平负
        half_full_cash_33_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_10 VARCHAR(10) COMMENT '赔率',
        -- 胜胜 ~ 负平
        half_full_cash_33_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_01 VARCHAR(10) COMMENT '赔率',
        -- 胜胜 ~ 负负
        half_full_cash_33_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_33_00 VARCHAR(10) COMMENT '赔率',
        
        -- 胜平 ~ 胜胜
        half_full_cash_31_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_33 VARCHAR(10) COMMENT '赔率',
        -- 胜平 ~ 胜平
        half_full_cash_31_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_31 VARCHAR(10) COMMENT '赔率',
        -- 胜平 ~ 平胜
        half_full_cash_31_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_13 VARCHAR(10) COMMENT '赔率',
        -- 胜平 ~ 平平
        half_full_cash_31_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_11 VARCHAR(10) COMMENT '赔率',
        -- 胜平 ~ 平负
        half_full_cash_31_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_10 VARCHAR(10) COMMENT '赔率',
        -- 胜平 ~ 负平
        half_full_cash_31_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_01 VARCHAR(10) COMMENT '赔率',
        -- 胜平 ~ 负负
        half_full_cash_31_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_31_00 VARCHAR(10) COMMENT '赔率',
        
        -- 平胜 ~ 胜胜
        half_full_cash_13_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_33 VARCHAR(10) COMMENT '赔率',
        -- 平胜 ~ 胜平
        half_full_cash_13_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_31 VARCHAR(10) COMMENT '赔率',
        -- 平胜 ~ 平胜
        half_full_cash_13_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_13 VARCHAR(10) COMMENT '赔率',
        -- 平胜 ~ 平平
        half_full_cash_13_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_11 VARCHAR(10) COMMENT '赔率',
        -- 平胜 ~ 平负
        half_full_cash_13_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_10 VARCHAR(10) COMMENT '赔率',
        -- 平胜 ~ 负平
        half_full_cash_13_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_01 VARCHAR(10) COMMENT '赔率',
        -- 平胜 ~ 负负
        half_full_cash_13_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_13_00 VARCHAR(10) COMMENT '赔率',
        
        -- 平平 ~ 胜胜
        half_full_cash_11_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_33 VARCHAR(10) COMMENT '赔率',
        -- 平平 ~ 胜平
        half_full_cash_11_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_31 VARCHAR(10) COMMENT '赔率',
        -- 平平 ~ 平胜
        half_full_cash_11_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_13 VARCHAR(10) COMMENT '赔率',
        -- 平平 ~ 平平
        half_full_cash_11_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_11 VARCHAR(10) COMMENT '赔率',
        -- 平平 ~ 平负
        half_full_cash_11_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_10 VARCHAR(10) COMMENT '赔率',
        -- 平平 ~ 负平
        half_full_cash_11_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_01 VARCHAR(10) COMMENT '赔率',
        -- 平平 ~ 负负
        half_full_cash_11_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_11_00 VARCHAR(10) COMMENT '赔率',
        
        -- 平负 ~ 胜胜
        half_full_cash_10_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_33 VARCHAR(10) COMMENT '赔率',
        -- 平负 ~ 胜平
        half_full_cash_10_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_31 VARCHAR(10) COMMENT '赔率',
        -- 平负 ~ 平胜
        half_full_cash_10_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_13 VARCHAR(10) COMMENT '赔率',
        -- 平负 ~ 平平
        half_full_cash_10_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_11 VARCHAR(10) COMMENT '赔率',
        -- 平负 ~ 平负
        half_full_cash_10_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_10 VARCHAR(10) COMMENT '赔率',
        -- 平负 ~ 负平
        half_full_cash_10_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_01 VARCHAR(10) COMMENT '赔率',
        -- 平负 ~ 负负
        half_full_cash_10_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_10_00 VARCHAR(10) COMMENT '赔率',
        
        -- 负平 ~ 胜胜
        half_full_cash_01_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_33 VARCHAR(10) COMMENT '赔率',
        -- 负平 ~ 胜平
        half_full_cash_01_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_31 VARCHAR(10) COMMENT '赔率',
        -- 负平 ~ 平胜
        half_full_cash_01_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_13 VARCHAR(10) COMMENT '赔率',
        -- 负平 ~ 平平
        half_full_cash_01_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_11 VARCHAR(10) COMMENT '赔率',
        -- 负平 ~ 平负
        half_full_cash_01_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_10 VARCHAR(10) COMMENT '赔率',
        -- 负平 ~ 负平
        half_full_cash_01_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_01 VARCHAR(10) COMMENT '赔率',
        -- 负平 ~ 负负
        half_full_cash_01_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_01_00 VARCHAR(10) COMMENT '赔率',
        
        -- 负负 ~ 胜胜
        half_full_cash_00_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_33 VARCHAR(10) COMMENT '赔率',
        -- 负负 ~ 胜平
        half_full_cash_00_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_31 VARCHAR(10) COMMENT '赔率',
        -- 负负 ~ 平胜
        half_full_cash_00_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_13 VARCHAR(10) COMMENT '赔率',
        -- 负负 ~ 平平
        half_full_cash_00_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_11 VARCHAR(10) COMMENT '赔率',
        -- 负负 ~ 平负
        half_full_cash_00_10 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_10 VARCHAR(10) COMMENT '赔率',
        -- 负负 ~ 负平
        half_full_cash_00_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_01 VARCHAR(10) COMMENT '赔率',
        -- 负负 ~ 负负
        half_full_cash_00_00 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        half_full_odds_00_00 VARCHAR(10) COMMENT '赔率',
        
        -- 总进球数 
        -- 1 ~ 1
        score_cash_11 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_11 VARCHAR(10) COMMENT '赔率',
        -- 1 ~ 2
        score_cash_12 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_12 VARCHAR(10) COMMENT '赔率',
        -- 1 ~ 3
        score_cash_13 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_13 VARCHAR(10) COMMENT '赔率',
        -- 1 ~ 4
        score_cash_14 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_14 VARCHAR(10) COMMENT '赔率',
        -- 2 ~ 1
        score_cash_21 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_21 VARCHAR(10) COMMENT '赔率',
        -- 2 ~ 2
        score_cash_22 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_22 VARCHAR(10) COMMENT '赔率',
        -- 2 ~ 3
        score_cash_23 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_23 VARCHAR(10) COMMENT '赔率',
        -- 2 ~ 4
        score_cash_24 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_24 VARCHAR(10) COMMENT '赔率',
        -- 3 ~ 1
        score_cash_31 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_31 VARCHAR(10) COMMENT '赔率',
        -- 3 ~ 2
        score_cash_32 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_32 VARCHAR(10) COMMENT '赔率',
        -- 3 ~ 3
        score_cash_33 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_33 VARCHAR(10) COMMENT '赔率',
        -- 3 ~ 4
        score_cash_34 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_34 VARCHAR(10) COMMENT '赔率',
        -- 4 ~ 1
        score_cash_41 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_41 VARCHAR(10) COMMENT '赔率',
        -- 4 ~ 2
        score_cash_42 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_42 VARCHAR(10) COMMENT '赔率',
        -- 4 ~ 3
        score_cash_43 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_43 VARCHAR(10) COMMENT '赔率',
        -- 4 ~ 4
        score_cash_44 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        score_odds_44 VARCHAR(10) COMMENT '赔率',
        
        -- 让球+平 
        -- 让胜 ~ 平
        handicap_draw_cash_3_1 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_odds_3_1 VARCHAR(10) COMMENT '赔率',
        -- 让平 ~ 平
        handicap_draw_cash_1_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_odds_1_01 VARCHAR(10) COMMENT '赔率',
        -- 让负 ~ 平
        handicap_draw_cash_0_1 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_odds_0_1 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 让胜
        handicap_draw_cash_1_3 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_odds_1_3 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 让平
        handicap_draw_cash_01_1 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_odds_01_1 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 让负
        handicap_draw_cash_1_0 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_odds_1_0 VARCHAR(10) COMMENT '赔率',
        
        -- 让球+平(特殊) 
        -- 让胜 ~ 平 (强队让球)
        handicap_draw_s_cash_3_1 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_s_odds_3_1 VARCHAR(10) COMMENT '赔率',
        -- 让平 ~ 平
        handicap_draw_s_cash_1_01 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_s_odds_1_01 VARCHAR(10) COMMENT '赔率',
        -- 让负 ~ 平
        handicap_draw_s_cash_0_1 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_s_odds_0_1 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 让胜
        handicap_draw_s_cash_1_3 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_s_odds_1_3 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 让平
        handicap_draw_s_cash_01_1 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_s_odds_01_1 VARCHAR(10) COMMENT '赔率',
        -- 平 ~ 让负
        handicap_draw_s_cash_1_0 VARCHAR(10) COMMENT '中奖金额:-100(未中奖,损失本金100元) ~ X(中奖金额)',
        handicap_draw_s_odds_1_0 VARCHAR(10) COMMENT '赔率',
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_jc_time (jc_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩足球购买结果数据表'
    """;

    /**
     * 获取SQL语句的方法
     */
    public static String getCreateEndTableSQL() {
        return CREATE_JC_End_BUY_RESULT_TABLE;
    }
}
