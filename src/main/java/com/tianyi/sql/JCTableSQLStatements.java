package com.tianyi.sql;

/**
 * 竞彩足球表的SQL语句,用于存储澳客中的竞彩足球比赛数据
 */
public class JCTableSQLStatements {

    /**
     * 创建竞彩足球表的SQL语句
     */
    public static final String CREATE_JC_TABLE = """
        CREATE TABLE IF NOT EXISTS jcTable (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            num VARCHAR(20) NOT NULL COMMENT '场次编号',
            jc_time VARCHAR(20) NOT NULL COMMENT '竞彩时间',
            match_time VARCHAR(20) NOT NULL COMMENT '比赛时间',
            league VARCHAR(100) NOT NULL COMMENT '联赛名称',
            home VARCHAR(100) NOT NULL COMMENT '主队名称',
            away VARCHAR(100) NOT NULL COMMENT '客队名称',
            
            -- 比赛结果相关字段
            half_score VARCHAR(10) COMMENT '半场比分',
            full_score VARCHAR(10) COMMENT '全场比分',
            
            -- 胜平负相关
            win_draw_lose_result VARCHAR(5) COMMENT '胜平负结果',
            win_draw_lose_odds VARCHAR(10) COMMENT '胜平负赔率',
            
            -- 让球胜平负相关
            handicap VARCHAR(10) COMMENT '让球数',
            handicap_result VARCHAR(5) COMMENT '让球胜平负结果',
            handicap_odds VARCHAR(10) COMMENT '让球胜平负赔率',
            
            -- 比分相关
            score_result VARCHAR(10) COMMENT '比分结果',
            score_odds VARCHAR(10) COMMENT '比分赔率',
            
            -- 总进球数相关
            goal_result VARCHAR(5) COMMENT '总进球数结果',
            goal_odds VARCHAR(10) COMMENT '总进球数赔率',
            
            -- 半全场相关
            half_full_result VARCHAR(10) COMMENT '半全场结果',
            half_full_odds VARCHAR(10) COMMENT '半全场赔率',
            
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_league (league),
            INDEX idx_match_time (match_time),
            INDEX idx_jc_time (jc_time),
            INDEX idx_num (num)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞彩足球比赛数据表'
        """;

    /**
     * 插入竞彩足球数据的SQL语句
     */
    public static final String INSERT_JC_TABLE = """
        INSERT INTO jcTable (
            num, jc_time, match_time, league, home, away,
            half_score, full_score, win_draw_lose_result, win_draw_lose_odds,
            handicap, handicap_result, handicap_odds,
            score_result, score_odds, goal_result, goal_odds,
            half_full_result, half_full_odds
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;

    /**
     * 查询所有竞彩数据的SQL语句
     */
    public static final String SELECT_ALL_JC_MATCHES = """
        SELECT * FROM jcTable ORDER BY jc_time DESC, match_time DESC
        """;

    /**
     * 根据联赛查询竞彩数据的SQL语句
     */
    public static final String SELECT_JC_MATCHES_BY_LEAGUE = """
        SELECT * FROM jcTable WHERE league = ? ORDER BY jc_time DESC, match_time DESC
        """;

    /**
     * 根据场次编号查询数据的SQL语句
     */
    public static final String SELECT_JC_MATCH_BY_NUM = """
        SELECT * FROM jcTable WHERE num = ?
        """;

    /**
     * 根据场次编号和竞彩时间查询数据的SQL语句
     */
    public static final String SELECT_JC_MATCH_BY_NUM_AND_JC_TIME = """
        SELECT * FROM jcTable
        WHERE num = ? AND jc_time = ?
        """;

    /**
     * 根据场次编号和竞彩时间查询数据的SQL语句
     */
    public static final String SELECT_JC_MATCH_BY_JC_TIME = """
        SELECT * FROM jcTable
        jc_time = ?
        """;

    /**
     * 根据竞彩时间范围查询数据的SQL语句
     */
    public static final String SELECT_JC_MATCHES_BY_JC_TIME_RANGE = """
        SELECT * FROM jcTable
        WHERE jc_time BETWEEN ? AND ?
        ORDER BY jc_time ASC, num ASC
        """;

    /**
     * 根据竞彩日期查询数据的SQL语句（查询某一天的所有比赛）
     */
    public static final String SELECT_JC_MATCHES_BY_JC_DATE = """
        SELECT * FROM jcTable
        WHERE DATE(jc_time) = ?
        ORDER BY jc_time ASC, num ASC
        """;

    /**
     * 根据场次编号和竞彩日期查询数据的SQL语句
     */
    public static final String SELECT_JC_MATCHES_BY_NUM_AND_JC_DATE = """
        SELECT * FROM jcTable
        WHERE num LIKE ? AND DATE(jc_time) = ?
        ORDER BY jc_time ASC, num ASC
        """;

    /**
     * 查询指定竞彩时间之后的比赛
     */
    public static final String SELECT_JC_MATCHES_AFTER_JC_TIME = """
        SELECT * FROM jcTable
        WHERE jc_time > ?
        ORDER BY jc_time ASC, num ASC
        """;

    /**
     * 查询指定竞彩时间之前的比赛
     */
    public static final String SELECT_JC_MATCHES_BEFORE_JC_TIME = """
        SELECT * FROM jcTable
        WHERE jc_time < ?
        ORDER BY jc_time DESC, num ASC
        """;

    /**
     * 根据场次编号和联赛查询数据的SQL语句
     */
    public static final String SELECT_JC_MATCHES_BY_NUM_AND_LEAGUE = """
        SELECT * FROM jcTable
        WHERE num = ? AND league = ?
        """;

    /**
     * 统计指定竞彩日期的比赛数量
     */
    public static final String COUNT_JC_MATCHES_BY_JC_DATE = """
        SELECT COUNT(*) as count
        FROM jcTable
        WHERE DATE(jc_time) = ?
        """;

    /**
     * 统计指定场次模式的比赛数量（如统计所有周三的比赛）
     */
    public static final String COUNT_JC_MATCHES_BY_NUM_PATTERN = """
        SELECT COUNT(*) as count
        FROM jcTable
        WHERE num LIKE ?
        """;

    /**
     * 统计竞彩数据总数的SQL语句
     */
    public static final String COUNT_JC_MATCHES = """
        SELECT COUNT(*) as count FROM jcTable
        """;

    /**
     * 删除所有竞彩数据的SQL语句（慎用）
     */
    public static final String DELETE_ALL_JC_MATCHES = """
        DELETE FROM jcTable
        """;

    /**
     * 删除竞彩表的SQL语句（危险操作）
     */
    public static final String DROP_JC_TABLE = """
        DROP TABLE IF EXISTS jcTable
        """;

    /**
     * 清空竞彩表数据但保留表结构
     */
    public static final String TRUNCATE_JC_TABLE = """
        TRUNCATE TABLE jcTable
        """;

    /**
     * 获取SQL语句的方法
     */
    public static String getCreateJCTableSQL() {
        return CREATE_JC_TABLE;
    }

    public static String getInsertJCTableSQL() {
        return INSERT_JC_TABLE;
    }

    public static String getSelectAllJCMatchesSQL() {
        return SELECT_ALL_JC_MATCHES;
    }

    public static String getCountJCMatchesSQL() {
        return COUNT_JC_MATCHES;
    }

    public static String getSelectJCMatchByNumAndJCTimeSQL() {
        return SELECT_JC_MATCH_BY_NUM_AND_JC_TIME;
    }

    public static String getSelectJCMatchByJCTimeSQL() {
        return SELECT_JC_MATCH_BY_JC_TIME;
    }

    public static String getSelectJCMatchesByJCTimeRangeSQL() {
        return SELECT_JC_MATCHES_BY_JC_TIME_RANGE;
    }

    public static String getSelectJCMatchesByJCDateSQL() {
        return SELECT_JC_MATCHES_BY_JC_DATE;
    }

    public static String getSelectJCMatchesByNumAndJCDateSQL() {
        return SELECT_JC_MATCHES_BY_NUM_AND_JC_DATE;
    }

    public static String getCountJCMatchesByJCDateSQL() {
        return COUNT_JC_MATCHES_BY_JC_DATE;
    }

    // 删除相关的SQL语句获取方法
    public static String getDeleteAllJCMatchesSQL() {
        return DELETE_ALL_JC_MATCHES;
    }

    public static String getDropJCTableSQL() {
        return DROP_JC_TABLE;
    }

    public static String getTruncateJCTableSQL() {
        return TRUNCATE_JC_TABLE;
    }
}
