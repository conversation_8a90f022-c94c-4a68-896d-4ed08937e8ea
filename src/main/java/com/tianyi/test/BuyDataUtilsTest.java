package com.tianyi.test;

import com.tianyi.util.BuyDataUtils;
import com.tianyi.util.DatabaseUtil;
import com.tianyi.database.DatabaseManager;
import java.math.BigDecimal;

/**
 * BuyDataUtils测试类
 * 演示购买数据工具类的各种功能
 */
public class BuyDataUtilsTest {
    
    public static void main(String[] args) {
        System.out.println("=== BuyDataUtils 功能测试 ===");
        System.out.println();
        
        // 检查数据库连接
        if (!DatabaseUtil.testConnection()) {
            System.err.println("❌ 数据库连接失败，请检查配置");
            return;
        }
        
        System.out.println("✅ 数据库连接正常");
        System.out.println();
        
        // 确保数据库和表已创建
        initializeDatabase();
        
        // 测试各种功能
        testInsertBuyRecords();
        testQueryBuyRecords();
        testConvenienceMethods();
        testUserStatistics();
        testUpdateAndDelete();
        
        System.out.println("=== BuyDataUtils 测试完成 ===");
    }
    
    /**
     * 初始化数据库
     */
    private static void initializeDatabase() {
        System.out.println("1. 初始化数据库...");
        
        if (!DatabaseManager.databaseExists()) {
            if (DatabaseManager.initializeDatabase()) {
                System.out.println("✅ 数据库初始化成功");
            } else {
                System.err.println("❌ 数据库初始化失败");
                return;
            }
        } else {
            System.out.println("✅ 数据库已存在");
        }
        
        System.out.println();
    }
    
    /**
     * 测试插入购买记录
     */
    private static void testInsertBuyRecords() {
        System.out.println("2. 测试插入购买记录...");
        
        // 测试完整参数插入
        boolean result1 = BuyDataUtils.insertBuyRecord(
            "user001", "1282037", "周六018",
            "2025-01-01 12:00", "01-01 00:45", "挪超",
            "特罗姆瑟", "瓦勒伦加", "胜平负", "主胜", "1.700",
            new BigDecimal("100.00"), "看好主队实力"
        );
        
        // 测试简化参数插入
        boolean result2 = BuyDataUtils.insertSimpleBuyRecord(
            "user002", "1282038", "周六019",
            "让球", "主队-1", "3.300", new BigDecimal("50.00")
        );
        
        System.out.println("完整参数插入结果: " + (result1 ? "成功" : "失败"));
        System.out.println("简化参数插入结果: " + (result2 ? "成功" : "失败"));
        
        // 插入示例数据
        BuyDataUtils.insertSampleBuyRecords();
        
        System.out.println();
    }
    
    /**
     * 测试查询购买记录
     */
    private static void testQueryBuyRecords() {
        System.out.println("3. 测试查询购买记录...");
        
        // 查询所有购买记录
        System.out.println("3.1 查询所有购买记录:");
        BuyDataUtils.showAllBuyRecords();
        
        // 查询特定用户的购买记录
        System.out.println("3.2 查询用户user001的购买记录:");
        BuyDataUtils.showUserBuyRecords("user001");
        
        // 查询特定比赛的购买记录
        System.out.println("3.3 查询比赛1282037的购买记录:");
        BuyDataUtils.showMatchBuyRecords("1282037");
        
        // 获取购买记录总数
        int totalCount = BuyDataUtils.getBuyRecordCount();
        System.out.println("购买记录总数: " + totalCount);
        
        System.out.println();
    }
    
    /**
     * 测试便捷投注方法
     */
    private static void testConvenienceMethods() {
        System.out.println("4. 测试便捷投注方法...");
        
        // 胜平负投注
        boolean winDrawLose = BuyDataUtils.betWinDrawLose(
            "user003", "1282040", "周日001", "客胜", "2.50", new BigDecimal("30.00")
        );
        System.out.println("胜平负投注: " + (winDrawLose ? "成功" : "失败"));
        
        // 让球投注
        boolean handicap = BuyDataUtils.betHandicap(
            "user003", "1282041", "周日002", "主队+1", "1.85", new BigDecimal("40.00")
        );
        System.out.println("让球投注: " + (handicap ? "成功" : "失败"));
        
        // 比分投注
        boolean score = BuyDataUtils.betScore(
            "user004", "1282042", "周日003", "1:0", "6.50", new BigDecimal("15.00")
        );
        System.out.println("比分投注: " + (score ? "成功" : "失败"));
        
        // 总进球投注
        boolean totalGoals = BuyDataUtils.betTotalGoals(
            "user004", "1282043", "周日004", "2球", "3.20", new BigDecimal("25.00")
        );
        System.out.println("总进球投注: " + (totalGoals ? "成功" : "失败"));
        
        // 半全场投注
        boolean halfFull = BuyDataUtils.betHalfFull(
            "user005", "1282044", "周日005", "胜-平", "8.00", new BigDecimal("10.00")
        );
        System.out.println("半全场投注: " + (halfFull ? "成功" : "失败"));
        
        System.out.println();
    }
    
    /**
     * 测试用户统计信息
     */
    private static void testUserStatistics() {
        System.out.println("5. 测试用户统计信息...");
        
        // 显示各用户的统计信息
        String[] users = {"user001", "user002", "user003", "user004", "user005"};
        
        for (String user : users) {
            BuyDataUtils.showUserStatistics(user);
        }
        
        System.out.println();
    }
    
    /**
     * 测试更新和删除操作
     */
    private static void testUpdateAndDelete() {
        System.out.println("6. 测试更新和删除操作...");
        
        // 模拟更新购买记录结果
        System.out.println("6.1 测试更新购买记录结果:");
        boolean updateResult = BuyDataUtils.updateBuyRecordResult(
            1, "2:1", "WON", new BigDecimal("170.00")
        );
        System.out.println("更新结果: " + (updateResult ? "成功" : "失败"));
        
        // 注意：删除操作在实际应用中要谨慎使用
        System.out.println("6.2 删除操作测试已跳过（避免误删数据）");
        
        System.out.println();
    }
    
    /**
     * 演示投注类型和状态枚举的使用
     */
    private static void demonstrateEnums() {
        System.out.println("7. 演示枚举类型...");
        
        System.out.println("投注类型:");
        for (BuyDataUtils.BetType betType : BuyDataUtils.BetType.values()) {
            System.out.println("  " + betType.name() + ": " + betType.getDescription());
        }
        
        System.out.println("投注状态:");
        for (BuyDataUtils.BetStatus betStatus : BuyDataUtils.BetStatus.values()) {
            System.out.println("  " + betStatus.name() + ": " + betStatus.getDescription());
        }
        
        System.out.println();
    }
    
    /**
     * 演示批量操作
     */
    private static void demonstrateBatchOperations() {
        System.out.println("8. 演示批量操作...");
        
        System.out.println("批量插入多个用户的投注记录...");
        
        // 用户A的多笔投注
        BuyDataUtils.betWinDrawLose("userA", "match001", "周一001", "主胜", "1.80", new BigDecimal("100"));
        BuyDataUtils.betHandicap("userA", "match002", "周一002", "主队-1", "2.10", new BigDecimal("50"));
        BuyDataUtils.betScore("userA", "match003", "周一003", "2:0", "7.50", new BigDecimal("20"));
        
        // 用户B的多笔投注
        BuyDataUtils.betTotalGoals("userB", "match001", "周一001", "3球", "3.40", new BigDecimal("30"));
        BuyDataUtils.betHalfFull("userB", "match004", "周一004", "平-胜", "12.00", new BigDecimal("10"));
        
        System.out.println("✅ 批量操作完成");
        
        // 显示最新的购买记录
        System.out.println("最新的购买记录:");
        BuyDataUtils.showAllBuyRecords();
        
        System.out.println();
    }
}
