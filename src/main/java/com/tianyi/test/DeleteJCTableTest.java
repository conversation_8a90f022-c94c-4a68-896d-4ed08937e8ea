package com.tianyi.test;

import com.tianyi.util.JCTableManager;
import com.tianyi.util.DatabaseUtil;
import com.tianyi.sql.SQLStatements;

/**
 * 删除竞彩表测试类
 * 演示各种删除竞彩表的操作
 */
public class DeleteJCTableTest {
    
    public static void main(String[] args) {
        System.out.println("=== 删除竞彩表测试 ===");
        System.out.println();
        
        // 检查数据库连接
        if (!DatabaseUtil.testConnection()) {
            System.err.println("❌ 数据库连接失败，请检查配置");
            return;
        }
        
        System.out.println("✅ 数据库连接正常");
        System.out.println();
        
        // 显示所有可用的删除SQL语句
        demonstrateDeleteSQLStatements();
        
        // 显示当前表状态
        JCTableManager.showJCTableStatus();
        
        // 提供交互式菜单
        System.out.println("是否要进入交互式管理菜单？(y/n)");
        java.util.Scanner scanner = new java.util.Scanner(System.in);
        String response = scanner.nextLine().trim().toLowerCase();
        
        if ("y".equals(response) || "yes".equals(response)) {
            JCTableManager.interactiveDeleteMenu();
        } else {
            System.out.println("测试结束");
        }
    }
    
    /**
     * 演示所有删除相关的SQL语句
     */
    private static void demonstrateDeleteSQLStatements() {
        System.out.println("=== 删除竞彩表相关的SQL语句 ===");
        System.out.println();
        
        System.out.println("1. 删除单条记录（根据match_id）：");
        System.out.println(SQLStatements.getDeleteJCMatchSQL());
        System.out.println("用途：删除指定比赛ID的记录");
        System.out.println("参数：match_id");
        System.out.println();
        
        System.out.println("2. 删除所有数据（保留表结构）：");
        System.out.println(SQLStatements.getDeleteAllJCMatchesSQL());
        System.out.println("用途：删除表中所有数据，但保留表结构");
        System.out.println("参数：无");
        System.out.println();
        
        System.out.println("3. 清空表（TRUNCATE）：");
        System.out.println(SQLStatements.getTruncateJCTableSQL());
        System.out.println("用途：快速清空表数据并重置自增ID");
        System.out.println("参数：无");
        System.out.println();
        
        System.out.println("4. 删除整个表（危险操作）：");
        System.out.println(SQLStatements.getDropJCTableSQL());
        System.out.println("用途：完全删除表结构和数据");
        System.out.println("参数：无");
        System.out.println();
        
        System.out.println("5. 重置自增ID：");
        System.out.println(SQLStatements.getResetJCTableAutoIncrementSQL());
        System.out.println("用途：将自增ID重置为1");
        System.out.println("参数：无");
        System.out.println();
        
        System.out.println("=== SQL语句演示完成 ===");
        System.out.println();
    }
    
    /**
     * 演示安全的删除操作流程
     */
    public static void demonstrateSafeDeleteProcess() {
        System.out.println("=== 安全删除操作流程演示 ===");
        System.out.println();
        
        System.out.println("推荐的删除操作流程：");
        System.out.println("1. 首先查看表状态和数据量");
        System.out.println("2. 如果需要备份，先导出数据");
        System.out.println("3. 根据需求选择合适的删除方式：");
        System.out.println("   - 删除部分数据：使用带WHERE条件的DELETE");
        System.out.println("   - 清空所有数据：使用TRUNCATE（快速）或DELETE（可回滚）");
        System.out.println("   - 删除整个表：使用DROP TABLE（谨慎使用）");
        System.out.println("4. 执行删除操作前进行二次确认");
        System.out.println("5. 删除后验证结果");
        System.out.println();
        
        System.out.println("各种删除方式的对比：");
        System.out.println("┌─────────────┬──────────┬──────────┬──────────┬──────────┐");
        System.out.println("│ 操作类型    │ 速度     │ 可回滚   │ 重置ID   │ 保留结构 │");
        System.out.println("├─────────────┼──────────┼──────────┼──────────┼──────────┤");
        System.out.println("│ DELETE      │ 慢       │ 是       │ 否       │ 是       │");
        System.out.println("│ TRUNCATE    │ 快       │ 否       │ 是       │ 是       │");
        System.out.println("│ DROP TABLE  │ 最快     │ 否       │ N/A      │ 否       │");
        System.out.println("└─────────────┴──────────┴──────────┴──────────┴──────────┘");
        System.out.println();
    }
    
    /**
     * 演示批量删除操作
     */
    public static void demonstrateBatchDelete() {
        System.out.println("=== 批量删除操作示例 ===");
        System.out.println();
        
        System.out.println("常用的批量删除SQL语句：");
        System.out.println();
        
        System.out.println("1. 删除指定联赛的所有比赛：");
        System.out.println("DELETE FROM jcTable WHERE league = ?");
        System.out.println();
        
        System.out.println("2. 删除指定日期之前的比赛：");
        System.out.println("DELETE FROM jcTable WHERE jc_time < ?");
        System.out.println();
        
        System.out.println("3. 删除指定场次模式的比赛：");
        System.out.println("DELETE FROM jcTable WHERE num LIKE ?");
        System.out.println();
        
        System.out.println("4. 删除指定时间范围的比赛：");
        System.out.println("DELETE FROM jcTable WHERE jc_time BETWEEN ? AND ?");
        System.out.println();
        
        System.out.println("5. 删除没有结果的比赛：");
        System.out.println("DELETE FROM jcTable WHERE full_score IS NULL OR full_score = ''");
        System.out.println();
    }
}
