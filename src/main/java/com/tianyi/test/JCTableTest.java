package com.tianyi.test;

import com.tianyi.dao.JCTableDAO;
import com.tianyi.database.DatabaseManager;

/**
 * jcTable测试类
 * 演示如何使用竞彩足球数据表
 */
public class JCTableTest {
    
    public static void main(String[] args) {
        System.out.println("=== jcTable 竞彩足球数据表测试 ===");
        System.out.println();
        
        // 确保数据库和表已创建
        if (!DatabaseManager.databaseExists()) {
            System.out.println("数据库不存在，开始初始化...");
            if (!DatabaseManager.initializeDatabase()) {
                System.err.println("数据库初始化失败！");
                return;
            }
        }
        
        // 创建DAO实例
        JCTableDAO dao = new JCTableDAO();
        
        // 测试插入数据（基于您提供的JSON数据）
        System.out.println("1. 测试插入比赛数据...");
        boolean insertResult = dao.insertMatch(
            "周六018",           // num
            "06-01 00:45",       // match_time
            "1282037",           // match_id
            "挪超",              // league
            "特罗姆瑟",          // home
            "瓦勒伦加",          // away
            "0-0",               // half_score
            "2-1",               // full_score
            "3",                 // win_draw_lose_result
            "1.700",             // win_draw_lose_odds
            "-1",                // handicap
            "1",                 // handicap_result
            "3.300",             // handicap_odds
            "2:1",               // score_result
            "7.000",             // score_odds
            "3",                 // goal_result
            "3.500",             // goal_odds
            "1-3",               // half_full_result
            "4.000"              // half_full_odds
        );
        
        if (insertResult) {
            System.out.println("✓ 比赛数据插入成功！");
        } else {
            System.out.println("✗ 比赛数据插入失败！");
        }
        System.out.println();
        
        // 测试插入更多示例数据
        System.out.println("2. 插入更多示例数据...");
        insertSampleData(dao);
        System.out.println();
        
        // 测试查询单个比赛
        System.out.println("3. 测试查询单个比赛数据...");
        dao.getMatchById("1282037");
        System.out.println();
        
        // 测试查询所有比赛
        System.out.println("4. 测试查询所有比赛数据...");
        dao.getAllMatches();
        System.out.println();
        
        // 测试按联赛查询
        System.out.println("5. 测试按联赛查询比赛数据...");
        dao.getMatchesByLeague("挪超");
        System.out.println();
        
        // 测试统计记录数
        System.out.println("6. 测试统计记录数...");
        int count = dao.getMatchCount();
        System.out.println("当前表中共有 " + count + " 条比赛记录");
        System.out.println();
        
        System.out.println("=== jcTable 测试完成 ===");
    }
    
    /**
     * 插入一些示例数据
     */
    private static void insertSampleData(JCTableDAO dao) {
        // 示例数据1
        dao.insertMatch(
            "周六019", "06-01 03:00", "1282038", "英超",
            "曼联", "利物浦", "1-1", "2-3",
            "1", "2.100", "0", "1", "1.850",
            "2:3", "12.000", "5", "2.800", "1-1", "6.500"
        );

        // 示例数据2
        dao.insertMatch(
            "周六020", "06-01 21:00", "1282039", "西甲",
            "皇马", "巴萨", "0-1", "1-2",
            "1", "1.950", "+1", "3", "2.200",
            "1:2", "8.500", "3", "3.200", "3-1", "15.000"
        );

        // 示例数据3
        dao.insertMatch(
            "周日001", "06-02 02:30", "1282040", "德甲",
            "拜仁", "多特", "2-0", "3-1",
            "3", "1.650", "-1", "3", "2.750",
            "3:1", "9.000", "4", "2.650", "3-3", "8.000"
        );

        System.out.println("✓ 示例数据插入完成");
    }
}
