package com.tianyi.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tianyi.model.MatchData;
import com.tianyi.util.JsonUtil;

import java.io.File;
import java.util.List;

/**
 * JSON调试测试类
 * 用于诊断和解决JSON反序列化问题
 */
public class JsonDebugTest {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) {
        String jsonFilePath = "/Users/<USER>/Desktop/JAVA/TianyiPro/sports_db/code/data/2025/2025年1月/01-03.json";
        
        System.out.println("=== JSON调试测试 ===");
        System.out.println("文件路径: " + jsonFilePath);
        System.out.println();
        
        // 1. 检查文件是否存在
        checkFileExists(jsonFilePath);
        
        // 2. 检查JSON格式
        checkJsonFormat(jsonFilePath);
        
        // 3. 分析JSON结构
        analyzeJsonStructure(jsonFilePath);
        
        // 4. 逐个解析元素
        parseElementsIndividually(jsonFilePath);
        
        // 5. 使用改进的JsonUtil测试
        testImprovedJsonUtil(jsonFilePath);
    }
    
    /**
     * 检查文件是否存在
     */
    private static void checkFileExists(String filePath) {
        System.out.println("1. 检查文件是否存在...");
        File file = new File(filePath);
        if (file.exists()) {
            System.out.println("✅ 文件存在");
            System.out.println("   文件大小: " + file.length() + " 字节");
        } else {
            System.out.println("❌ 文件不存在");
            return;
        }
        System.out.println();
    }
    
    /**
     * 检查JSON格式
     */
    private static void checkJsonFormat(String filePath) {
        System.out.println("2. 检查JSON格式...");
        try {
            JsonNode rootNode = objectMapper.readTree(new File(filePath));
            System.out.println("✅ JSON格式正确");
            System.out.println("   根节点类型: " + (rootNode.isArray() ? "数组" : "对象"));
            if (rootNode.isArray()) {
                System.out.println("   数组长度: " + rootNode.size());
            }
        } catch (Exception e) {
            System.out.println("❌ JSON格式错误: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 分析JSON结构
     */
    private static void analyzeJsonStructure(String filePath) {
        System.out.println("3. 分析JSON结构...");
        try {
            JsonNode rootNode = objectMapper.readTree(new File(filePath));
            
            if (rootNode.isArray()) {
                for (int i = 0; i < Math.min(3, rootNode.size()); i++) {
                    JsonNode element = rootNode.get(i);
                    System.out.println("   元素 " + i + ":");
                    System.out.println("     类型: " + getNodeType(element));
                    
                    if (element.isObject()) {
                        System.out.println("     字段数量: " + element.size());
                        element.fieldNames().forEachRemaining(fieldName -> 
                            System.out.println("       - " + fieldName + ": " + getNodeType(element.get(fieldName)))
                        );
                    } else {
                        System.out.println("     值: " + element.toString());
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("❌ 分析JSON结构失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 逐个解析元素
     */
    private static void parseElementsIndividually(String filePath) {
        System.out.println("4. 逐个解析元素...");
        try {
            JsonNode rootNode = objectMapper.readTree(new File(filePath));
            
            // 配置ObjectMapper
            objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            
            if (rootNode.isArray()) {
                int successCount = 0;
                int failCount = 0;
                
                for (int i = 0; i < rootNode.size(); i++) {
                    JsonNode element = rootNode.get(i);
                    
                    try {
                        if (element.isObject()) {
                            MatchData match = objectMapper.treeToValue(element, MatchData.class);
                            if (match != null) {
                                successCount++;
                                if (i < 3) { // 只显示前3个
                                    System.out.println("   ✅ 元素 " + i + " 解析成功: " + match.getNum() + " - " + match.getHome() + " vs " + match.getAway());
                                }
                            }
                        } else {
                            System.out.println("   ❌ 元素 " + i + " 不是对象: " + element.toString());
                            failCount++;
                        }
                    } catch (Exception e) {
                        System.out.println("   ❌ 元素 " + i + " 解析失败: " + e.getMessage());
                        System.out.println("       元素内容: " + element.toString());
                        failCount++;
                    }
                }
                
                System.out.println("   解析结果: 成功 " + successCount + " 个，失败 " + failCount + " 个");
            }
        } catch (Exception e) {
            System.out.println("❌ 逐个解析失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 使用改进的JsonUtil测试
     */
    private static void testImprovedJsonUtil(String filePath) {
        System.out.println("5. 使用改进的JsonUtil测试...");
        try {
            List<MatchData> matchList = JsonUtil.readMatchDataFromFile(filePath);
            
            if (matchList != null && !matchList.isEmpty()) {
                System.out.println("✅ JsonUtil解析成功");
                System.out.println("   解析到 " + matchList.size() + " 个比赛数据");
                
                // 显示前3个比赛的基本信息
                for (int i = 0; i < Math.min(3, matchList.size()); i++) {
                    MatchData match = matchList.get(i);
                    System.out.println("   比赛 " + (i + 1) + ": " + match.getNum() + " - " + 
                                     match.getHome() + " vs " + match.getAway() + " (" + match.getLeague() + ")");
                }
            } else {
                System.out.println("❌ JsonUtil解析失败或返回空数据");
            }
        } catch (Exception e) {
            System.out.println("❌ JsonUtil测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
    }
    
    /**
     * 获取节点类型描述
     */
    private static String getNodeType(JsonNode node) {
        if (node.isObject()) return "对象";
        if (node.isArray()) return "数组";
        if (node.isTextual()) return "字符串";
        if (node.isNumber()) return "数字";
        if (node.isBoolean()) return "布尔值";
        if (node.isNull()) return "null";
        return "未知";
    }
}
