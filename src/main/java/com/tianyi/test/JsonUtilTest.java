package com.tianyi.test;

import com.tianyi.util.JsonUtil;
import com.tianyi.model.MatchData;
import java.util.List;

/**
 * JsonUtil测试类
 * 用于验证JsonUtil是否正常工作
 */
public class JsonUtilTest {
    
    public static void main(String[] args) {
        System.out.println("=== JsonUtil 测试 ===");
        
        // JSON文件路径
        String jsonFilePath = "/Users/<USER>/Desktop/JAVA/TianyiPro/sports_db/code/data/2025/2025年1月/01-03.json";
        
        try {
            // 1. 测试文件验证
            System.out.println("1. 测试文件验证...");
            boolean isValid = JsonUtil.validateJsonFile(jsonFilePath);
            System.out.println("文件验证结果: " + (isValid ? "成功" : "失败"));
            System.out.println();
            
            if (!isValid) {
                System.err.println("文件验证失败，测试终止");
                return;
            }
            
            // 2. 测试读取数据
            System.out.println("2. 测试读取数据...");
            List<MatchData> matchList = JsonUtil.readMatchDataFromFile(jsonFilePath);
            
            if (matchList == null) {
                System.err.println("读取数据失败");
                return;
            }
            
            System.out.println("成功读取 " + matchList.size() + " 场比赛数据");
            System.out.println();
            
            // 3. 测试打印摘要
            System.out.println("3. 测试打印摘要...");
            JsonUtil.printMatchSummary(matchList);
            
            // 4. 测试联赛统计
            System.out.println("4. 测试联赛统计...");
            JsonUtil.printLeagueStatistics(matchList);
            
            // 5. 测试详细信息打印
            System.out.println("5. 测试详细信息打印（前2场）...");
            JsonUtil.printDetailedMatches(matchList, 2);
            
            // 6. 测试筛选功能
            System.out.println("6. 测试筛选功能（澳超）...");
            List<MatchData> ausMatches = JsonUtil.filterByLeague(matchList, "澳超");
            if (ausMatches != null) {
                System.out.println("找到 " + ausMatches.size() + " 场澳超比赛");
                for (MatchData match : ausMatches) {
                    System.out.println("  - " + match.toString());
                }
            }
            System.out.println();
            
            // 7. 测试查找功能
            System.out.println("7. 测试查找功能...");
            MatchData foundMatch = JsonUtil.findByMatchId(matchList, "1279149");
            if (foundMatch != null) {
                System.out.println("找到比赛: " + foundMatch.toString());
            } else {
                System.out.println("未找到指定ID的比赛");
            }
            
            System.out.println();
            System.out.println("=== JsonUtil 测试完成 ===");
            System.out.println("✅ 所有功能正常工作！");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
