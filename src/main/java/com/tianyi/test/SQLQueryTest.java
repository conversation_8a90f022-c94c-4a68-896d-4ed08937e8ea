package com.tianyi.test;

import com.tianyi.sql.SQLStatements;
import com.tianyi.util.DatabaseUtil;
import java.sql.*;

/**
 * SQL查询测试类
 * 演示利用场次编号与竞彩时间查询表的SQL语句
 */
public class SQLQueryTest {
    
    public static void main(String[] args) {
        System.out.println("=== SQL查询测试 ===");
        System.out.println();
        
        // 演示各种SQL查询语句
        demonstrateSQLQueries();
        
        // 如果数据库连接正常，执行实际查询
        if (DatabaseUtil.testConnection()) {
            System.out.println("数据库连接正常，执行实际查询测试...");
            executeActualQueries();
        } else {
            System.out.println("数据库连接失败，跳过实际查询测试");
        }
    }
    
    /**
     * 演示各种SQL查询语句
     */
    private static void demonstrateSQLQueries() {
        System.out.println("=== 场次编号与竞彩时间相关的SQL查询语句 ===");
        System.out.println();
        
        // 1. 根据场次编号查询
        System.out.println("1. 根据场次编号查询：");
        System.out.println(SQLStatements.getSelectJCMatchByNumSQL());
        System.out.println("使用示例：查询场次为'周三001'的比赛");
        System.out.println("参数：num = '周三001'");
        System.out.println();
        
        // 2. 根据场次编号和竞彩时间查询
        System.out.println("2. 根据场次编号和竞彩时间查询：");
        System.out.println(SQLStatements.getSelectJCMatchByNumAndJCTimeSQL());
        System.out.println("使用示例：查询场次为'周三001'且竞彩时间为'2025-01-01 12:00'的比赛");
        System.out.println("参数：num = '周三001', jc_time = '2025-01-01 12:00'");
        System.out.println();
        
        // 3. 根据场次编号模糊查询
        System.out.println("3. 根据场次编号模糊查询（查询所有周三的比赛）：");
        System.out.println(SQLStatements.getSelectJCMatchesByNumPatternSQL());
        System.out.println("使用示例：查询所有周三的比赛");
        System.out.println("参数：num_pattern = '周三%'");
        System.out.println();
        
        // 4. 根据竞彩时间范围查询
        System.out.println("4. 根据竞彩时间范围查询：");
        System.out.println(SQLStatements.getSelectJCMatchesByJCTimeRangeSQL());
        System.out.println("使用示例：查询2025-01-01到2025-01-07之间的比赛");
        System.out.println("参数：start_time = '2025-01-01 00:00', end_time = '2025-01-07 23:59'");
        System.out.println();
        
        // 5. 根据竞彩日期查询
        System.out.println("5. 根据竞彩日期查询（查询某一天的所有比赛）：");
        System.out.println(SQLStatements.getSelectJCMatchesByJCDateSQL());
        System.out.println("使用示例：查询2025-01-01当天的所有比赛");
        System.out.println("参数：jc_date = '2025-01-01'");
        System.out.println();
        
        // 6. 根据场次编号和竞彩日期查询
        System.out.println("6. 根据场次编号和竞彩日期查询：");
        System.out.println(SQLStatements.getSelectJCMatchesByNumAndJCDateSQL());
        System.out.println("使用示例：查询2025-01-01当天所有周三的比赛");
        System.out.println("参数：num_pattern = '周三%', jc_date = '2025-01-01'");
        System.out.println();
        
        // 7. 查询指定竞彩时间之后的比赛
        System.out.println("7. 查询指定竞彩时间之后的比赛：");
        System.out.println(SQLStatements.getSelectJCMatchesAfterJCTimeSQL());
        System.out.println("使用示例：查询2025-01-01 12:00之后的比赛");
        System.out.println("参数：jc_time = '2025-01-01 12:00'");
        System.out.println();
        
        // 8. 查询指定竞彩时间之前的比赛
        System.out.println("8. 查询指定竞彩时间之前的比赛：");
        System.out.println(SQLStatements.getSelectJCMatchesBeforeJCTimeSQL());
        System.out.println("使用示例：查询2025-01-01 12:00之前的比赛");
        System.out.println("参数：jc_time = '2025-01-01 12:00'");
        System.out.println();
        
        // 9. 统计查询
        System.out.println("9. 统计指定竞彩日期的比赛数量：");
        System.out.println(SQLStatements.getCountJCMatchesByJCDateSQL());
        System.out.println("使用示例：统计2025-01-01当天的比赛数量");
        System.out.println("参数：jc_date = '2025-01-01'");
        System.out.println();
        
        System.out.println("10. 统计指定场次模式的比赛数量：");
        System.out.println(SQLStatements.getCountJCMatchesByNumPatternSQL());
        System.out.println("使用示例：统计所有周三的比赛数量");
        System.out.println("参数：num_pattern = '周三%'");
        System.out.println();
    }
    
    /**
     * 执行实际的数据库查询测试
     */
    private static void executeActualQueries() {
        System.out.println("=== 实际查询测试 ===");
        
        // 测试查询所有数据
        testSelectAllMatches();
        
        // 测试按场次编号模糊查询
        testSelectByNumPattern();
        
        // 测试统计查询
        testCountQueries();
    }
    
    /**
     * 测试查询所有比赛
     */
    private static void testSelectAllMatches() {
        System.out.println("1. 查询所有比赛数据：");
        
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();
            resultSet = statement.executeQuery(SQLStatements.getSelectAllJCMatchesSQL());
            
            int count = 0;
            while (resultSet.next() && count < 5) { // 只显示前5条
                System.out.printf("  场次: %s, 竞彩时间: %s, 主队: %s vs 客队: %s%n",
                    resultSet.getString("num"),
                    resultSet.getString("jc_time"),
                    resultSet.getString("home"),
                    resultSet.getString("away")
                );
                count++;
            }
            
            if (count == 0) {
                System.out.println("  暂无数据");
            }
            
        } catch (SQLException e) {
            System.out.println("  查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        System.out.println();
    }
    
    /**
     * 测试按场次编号模糊查询
     */
    private static void testSelectByNumPattern() {
        System.out.println("2. 按场次编号模糊查询（查询所有周三的比赛）：");
        
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(SQLStatements.getSelectJCMatchesByNumPatternSQL());
            statement.setString(1, "周三%");
            
            resultSet = statement.executeQuery();
            
            int count = 0;
            while (resultSet.next()) {
                System.out.printf("  场次: %s, 竞彩时间: %s, 联赛: %s, %s vs %s%n",
                    resultSet.getString("num"),
                    resultSet.getString("jc_time"),
                    resultSet.getString("league"),
                    resultSet.getString("home"),
                    resultSet.getString("away")
                );
                count++;
            }
            
            if (count == 0) {
                System.out.println("  暂无周三的比赛数据");
            } else {
                System.out.println("  共找到 " + count + " 场周三的比赛");
            }
            
        } catch (SQLException e) {
            System.out.println("  查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        System.out.println();
    }
    
    /**
     * 测试统计查询
     */
    private static void testCountQueries() {
        System.out.println("3. 统计查询测试：");
        
        // 统计总比赛数量
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();
            resultSet = statement.executeQuery(SQLStatements.getCountJCMatchesSQL());
            
            if (resultSet.next()) {
                int totalCount = resultSet.getInt("count");
                System.out.println("  总比赛数量: " + totalCount);
            }
            
        } catch (SQLException e) {
            System.out.println("  统计查询失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeResources(connection, statement, resultSet);
        }
        
        System.out.println();
        System.out.println("=== SQL查询测试完成 ===");
    }
}
