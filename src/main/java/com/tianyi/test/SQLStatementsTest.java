package com.tianyi.test;

import com.tianyi.database.DatabaseManager;
import com.tianyi.util.JCDataUtil;
import com.tianyi.sql.SQLStatements;

/**
 * SQL语句处理类测试
 * 验证新的SQL语句处理类和竞彩时间字段功能
 */
public class SQLStatementsTest {
    
    public static void main(String[] args) {
        System.out.println("=== SQL语句处理类测试 ===");
        System.out.println();
        
        // 1. 显示SQL语句
        System.out.println("1. 显示创建表的SQL语句：");
        System.out.println(SQLStatements.getCreateJCTableSQL());
        System.out.println();
        
        // 2. 确保数据库和表已创建
        System.out.println("2. 初始化数据库...");
        if (!DatabaseManager.databaseExists()) {
            if (!DatabaseManager.initializeDatabase()) {
                System.err.println("数据库初始化失败！");
                return;
            }
        }
        System.out.println("✓ 数据库初始化完成");
        System.out.println();
        
        // 3. 测试插入数据（包含竞彩时间）
        System.out.println("3. 测试插入数据（包含竞彩时间字段）...");
        boolean insertResult = JCDataUtil.insertMatchData(
            "周六018",              // num
            "2024-06-01 12:00",     // jc_time (竞彩时间)
            "06-01 00:45",          // match_time (比赛时间)
            "1282037",              // matchId
            "挪超",                 // league
            "特罗姆瑟",             // home
            "瓦勒伦加",             // away
            "0-0",                  // half
            "2-1",                  // full
            "3",                    // win_draw_lose_result
            "1.700",                // win_draw_lose_odds
            "-1",                   // handicap
            "1",                    // handicap_result
            "3.300",                // handicap_odds
            "2:1",                  // score_result
            "7.000",                // score_odds
            "3",                    // goal_result
            "3.500",                // goal_odds
            "1-3",                  // half_full_result
            "4.000"                 // half_full_odds
        );
        
        if (insertResult) {
            System.out.println("✓ 数据插入成功！");
        } else {
            System.out.println("✗ 数据插入失败！");
        }
        System.out.println();
        
        // 4. 测试自动生成竞彩时间的插入方法
        System.out.println("4. 测试自动生成竞彩时间的插入方法...");
        boolean autoInsertResult = JCDataUtil.insertMatchDataWithAutoJCTime(
            "周六019",              // num
            "06-01 03:00",          // match_time
            "1282038",              // matchId
            "英超",                 // league
            "曼联",                 // home
            "利物浦",               // away
            "1-1",                  // half
            "2-3",                  // full
            "1",                    // win_draw_lose_result
            "2.100",                // win_draw_lose_odds
            "0",                    // handicap
            "1",                    // handicap_result
            "1.850",                // handicap_odds
            "2:3",                  // score_result
            "12.000",               // score_odds
            "5",                    // goal_result
            "2.800",                // goal_odds
            "1-1",                  // half_full_result
            "6.500"                 // half_full_odds
        );
        
        if (autoInsertResult) {
            System.out.println("✓ 自动竞彩时间数据插入成功！");
        } else {
            System.out.println("✗ 自动竞彩时间数据插入失败！");
        }
        System.out.println();
        
        // 5. 查询并显示数据
        System.out.println("5. 查询并显示所有数据...");
        JCDataUtil.showAllMatches();
        System.out.println();
        
        // 6. 根据比赛ID查询详细信息
        System.out.println("6. 查询比赛ID为1282037的详细信息...");
        JCDataUtil.showMatchById("1282037");
        System.out.println();
        
        // 7. 统计数据
        System.out.println("7. 统计信息...");
        int totalCount = JCDataUtil.getMatchCount();
        System.out.println("当前数据库中共有 " + totalCount + " 条比赛记录");
        System.out.println();
        
        // 8. 显示所有可用的SQL语句
        System.out.println("8. 可用的SQL语句列表：");
        System.out.println("- CREATE_JC_TABLE: 创建竞彩表");
        System.out.println("- INSERT_JC_TABLE: 插入竞彩数据");
        System.out.println("- SELECT_ALL_JC_MATCHES: 查询所有比赛");
        System.out.println("- SELECT_JC_MATCH_BY_ID: 根据ID查询比赛");
        System.out.println("- SELECT_JC_MATCHES_BY_LEAGUE: 根据联赛查询");
        System.out.println("- SELECT_JC_MATCHES_BY_JC_TIME: 根据竞彩时间查询");
        System.out.println("- UPDATE_JC_MATCH: 更新比赛数据");
        System.out.println("- DELETE_JC_MATCH: 删除比赛数据");
        System.out.println("- COUNT_JC_MATCHES: 统计比赛数量");
        System.out.println();
        
        System.out.println("=== SQL语句处理类测试完成 ===");
        System.out.println("✓ 竞彩时间字段已成功添加到jcTable");
        System.out.println("✓ SQL语句处理类工作正常");
    }
}
