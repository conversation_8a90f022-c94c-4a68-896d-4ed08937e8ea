package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.dao.BuyTableType;
import com.tianyi.model.MatchData;

import java.util.*;

/**
 * 购买数据工具类
 * 提供便捷的购买记录操作方法，参照JCDataUtils格式
 */
public class BuyDataUtils {

    static Map<String, Integer> halfFullIndexMap = new HashMap<String, Integer>() {{
        // 半全场玩法参数 - 第一组 (33-xx)
        put("3333", 0);
        put("3331", 1);
        put("3313", 2);
        put("3311", 3);
        put("3310", 4);
        put("3301", 5);
        put("3300", 6);

        // 半全场玩法参数 - 第二组 (31-xx)
        put("3133", 7);
        put("3131", 8);
        put("3113", 9);
        put("3111", 10);
        put("3110", 11);
        put("3101", 12);
        put("3100", 13);

        // 半全场玩法参数 - 第三组 (13-xx)
        put("1333", 14);
        put("1331", 15);
        put("1313", 16);
        put("1311", 17);
        put("1310", 18);
        put("1301", 19);
        put("1300", 20);

        // 半全场玩法参数 - 第四组 (11-xx)
        put("1133", 21);
        put("1131", 22);
        put("1113", 23);
        put("1111", 24);
        put("1110", 25);
        put("1101", 26);
        put("1100", 27);

        // 半全场玩法参数 - 第五组 (10-xx)
        put("1033", 28);
        put("1031", 29);
        put("1013", 30);
        put("1011", 31);
        put("1010", 32);
        put("1001", 33);
        put("1000", 34);

        // 半全场玩法参数 - 第六组 (01-xx)
        put("0133", 35);
        put("0131", 36);
        put("0113", 37);
        put("0111", 38);
        put("0110", 39);
        put("0101", 40);
        put("0100", 41);

        // 半全场玩法参数 - 第七组 (00-xx)
        put("0033", 42);
        put("0031", 43);
        put("0013", 44);
        put("0011", 45);
        put("0010", 46);
        put("0001", 47);
        put("0000", 48);
    }};

    private static final BuyTableDAO dao = new BuyTableDAO();
    public static boolean insertBuyResultTable(BuyTableType type, String jcTime, String fNum, String sNum,
                                               // 胜平负玩法
                                               String winLoseCash11,

                                               // 让球玩法参数数组
                                               String[] handicapCashParams,

                                               // 半全场玩法参数数组
                                               String[] halfFullCashParams,

                                               // 总进球数玩法参数数组
                                               String[] scoreCashParams,

                                               // 让球+平玩法参数数组
                                               String[] handicapDrawCashParams) {

        // 验证参数数量
        if (handicapCashParams.length != 9) {
            throw new IllegalArgumentException("让球玩法参数数量应为9个");
        }

        if (scoreCashParams.length != 25) {
            throw new IllegalArgumentException("总进球数玩法参数数量应为25个");
        }

        if (halfFullCashParams.length != 49) {
            throw new IllegalArgumentException("半全场玩法参数数量应为49个");
        }

        if (handicapDrawCashParams.length != 6) {
            throw new IllegalArgumentException("让球+平玩法参数数量应为6个");
        }

        return dao.insertBuyResult(type, jcTime, fNum, sNum,
                // 胜平负玩法参数
                winLoseCash11,

                // 让球玩法参数
                handicapCashParams[0],  // handicapCash33
                handicapCashParams[1],  // handicapCash31
                handicapCashParams[2],  // handicapCash30
                handicapCashParams[3],  // handicapCash13
                handicapCashParams[4],  // handicapCash11
                handicapCashParams[5],  // handicapCash10
                handicapCashParams[6],  // handicapCash03
                handicapCashParams[7],  // handicapCash01
                handicapCashParams[8],  // handicapCash00

                // 半全场玩法参数 - 第一组 (33-xx)
                halfFullCashParams[0],   // halfFullCash3333
                halfFullCashParams[1],   // halfFullCash3331
                halfFullCashParams[2],   // halfFullCash3313
                halfFullCashParams[3],   // halfFullCash3311
                halfFullCashParams[4],   // halfFullCash3310
                halfFullCashParams[5],   // halfFullCash3301
                halfFullCashParams[6],   // halfFullCash3300

                // 半全场玩法参数 - 第二组 (31-xx)
                halfFullCashParams[7],   // halfFullCash3133
                halfFullCashParams[8],   // halfFullCash3131
                halfFullCashParams[9],   // halfFullCash3113
                halfFullCashParams[10],  // halfFullCash3111
                halfFullCashParams[11],  // halfFullCash3110
                halfFullCashParams[12],  // halfFullCash3101
                halfFullCashParams[13],  // halfFullCash3100

                // 半全场玩法参数 - 第三组 (13-xx)
                halfFullCashParams[14],  // halfFullCash1333
                halfFullCashParams[15],  // halfFullCash1331
                halfFullCashParams[16],  // halfFullCash1313
                halfFullCashParams[17],  // halfFullCash1311
                halfFullCashParams[18],  // halfFullCash1310
                halfFullCashParams[19],  // halfFullCash1301
                halfFullCashParams[20],  // halfFullCash1300

                // 半全场玩法参数 - 第四组 (11-xx)
                halfFullCashParams[21],  // halfFullCash1133
                halfFullCashParams[22],  // halfFullCash1131
                halfFullCashParams[23],  // halfFullCash1113
                halfFullCashParams[24],  // halfFullCash1111
                halfFullCashParams[25],  // halfFullCash1110
                halfFullCashParams[26],  // halfFullCash1101
                halfFullCashParams[27],  // halfFullCash1100

                // 半全场玩法参数 - 第五组 (10-xx)
                halfFullCashParams[28],  // halfFullCash1033
                halfFullCashParams[29],  // halfFullCash1031
                halfFullCashParams[30],  // halfFullCash1013
                halfFullCashParams[31],  // halfFullCash1011
                halfFullCashParams[32],  // halfFullCash1010
                halfFullCashParams[33],  // halfFullCash1001
                halfFullCashParams[34],  // halfFullCash1000

                // 半全场玩法参数 - 第六组 (01-xx)
                halfFullCashParams[35],  // halfFullCash0133
                halfFullCashParams[36],  // halfFullCash0131
                halfFullCashParams[37],  // halfFullCash0113
                halfFullCashParams[38],  // halfFullCash0111
                halfFullCashParams[39],  // halfFullCash0110
                halfFullCashParams[40],  // halfFullCash0101
                halfFullCashParams[41],  // halfFullCash0100

                // 半全场玩法参数 - 第七组 (00-xx)
                halfFullCashParams[42],  // halfFullCash0033
                halfFullCashParams[43],  // halfFullCash0031
                halfFullCashParams[44],  // halfFullCash0013
                halfFullCashParams[45],  // halfFullCash0011
                halfFullCashParams[46],  // halfFullCash0010
                halfFullCashParams[47],  // halfFullCash0001
                halfFullCashParams[48],  // halfFullCash0000

                // 总进球数玩法参数
                scoreCashParams[0],   // scoreCash00
                scoreCashParams[1],   // scoreCash01
                scoreCashParams[2],   // scoreCash02
                scoreCashParams[3],   // scoreCash03
                scoreCashParams[4],   // scoreCash04
                scoreCashParams[5],   // scoreCash10
                scoreCashParams[6],   // scoreCash11
                scoreCashParams[7],   // scoreCash12
                scoreCashParams[8],   // scoreCash13
                scoreCashParams[9],   // scoreCash14
                scoreCashParams[10],  // scoreCash20
                scoreCashParams[11],  // scoreCash21
                scoreCashParams[12],  // scoreCash22
                scoreCashParams[13],  // scoreCash23
                scoreCashParams[14],  // scoreCash24
                scoreCashParams[15],  // scoreCash30
                scoreCashParams[16],  // scoreCash31
                scoreCashParams[17],  // scoreCash32
                scoreCashParams[18],  // scoreCash33
                scoreCashParams[19],  // scoreCash34
                scoreCashParams[20],  // scoreCash40
                scoreCashParams[21],  // scoreCash41
                scoreCashParams[22],  // scoreCash42
                scoreCashParams[23],  // scoreCash43
                scoreCashParams[24],  // scoreCash44

                // 让球+平玩法参数
                handicapDrawCashParams[0],  // handicapDrawCash31
                handicapDrawCashParams[1],  // handicapDrawCash101
                handicapDrawCashParams[2],  // handicapDrawCash01
                handicapDrawCashParams[3],  // handicapDrawCash13
                handicapDrawCashParams[4],  // handicapDrawCash011
                handicapDrawCashParams[5]   // handicapDrawCash10
        );
    }

    public static void insertBuyResultData(BuyTableType type, MatchData match1, MatchData match2, String timeStr) {
        String jcDate = "";
        // 添加场次日期信息
        try {
            jcDate = TimeUtil.getJCDateFromWeekday(timeStr, match1.getMatchTime(), match1.getNum());
        } catch (Exception e) {
            System.out.println("场次日期: 无法转换 (" + e.getMessage() + ")");
        }
        String num1 = match1.getNum();
        String num2 = match2.getNum();
        String full1 = match1.getResult().getFull();
        String full2 = match2.getResult().getFull();
        if (!full1.isEmpty() && !full2.isEmpty()) {
            String winDrawLoseResult1 = match1.getResult().getWinDrawLoseResult();
            String winDrawLoseResult2 = match2.getResult().getWinDrawLoseResult();
            String winDrawLoseOdds1 = match1.getResult().getWinDrawLoseOdds();
            String winDrawLoseOdds2 = match2.getResult().getWinDrawLoseOdds();

            int handicapResult1 = Integer.parseInt(match1.getResult().getHandicapResult());
            int handicapResult2 = Integer.parseInt(match2.getResult().getHandicapResult());
            String handicapOdds1 = match1.getResult().getHandicapOdds();
            String handicapOdds2 = match2.getResult().getHandicapOdds();

            String[] scoreList1 = full1.split("-");
            String[] scoreList2 = full2.split("-");
            int scoreSum1 = Integer.parseInt(scoreList1[0]) + Integer.parseInt(scoreList1[1]);
            int scoreSum2 = Integer.parseInt(scoreList2[0]) + Integer.parseInt(scoreList2[1]);
            String scoreOdds1 = match1.getResult().getGoalOdds();
            String scoreOdds2 = match2.getResult().getGoalOdds();

            String halfFullResult1 = match1.getResult().getHalfFullResult();
            String halfFullResult2 = match2.getResult().getHalfFullResult();
            String halfFullOdds1 = match1.getResult().getHalfFullOdds();
            String halfFullOdds2 = match2.getResult().getHalfFullOdds();

            // 使用数组存储各种玩法的奖金结果
            String[] scoreCash = new String[25];
            String[] handicapCash = new String[9];
            String[] halfFullCash = new String[49];
            String[] handicapDrawCash = new String[6];

            // 初始化默认值
            initializeCashArrays(scoreCash, handicapCash, halfFullCash, handicapDrawCash);

            // 处理胜平负玩法（两个平的情况）
            String winLoseCash11 = "-100";
            if ("1".equals(winDrawLoseResult1) && "1".equals(winDrawLoseResult2)) {
                winLoseCash11 = calculateCash(winDrawLoseOdds1, winDrawLoseOdds2);
            }

            // 处理让球玩法
            String handicapCashResult = calculateCash(handicapOdds1, handicapOdds2);
            int handicapArrIndex = getHandicapArrIndex(handicapResult1, handicapResult2);
            if (handicapArrIndex != -1) {
                handicapCash[handicapArrIndex] = handicapCashResult;
            }

            // 处理总进球数玩法
            String scoreCashResult = calculateCash(scoreOdds1, scoreOdds2);
            int scoreArrIndex = getScoreArrIndex(scoreSum1, scoreSum2);
            if (scoreArrIndex != -1) {
                scoreCash[scoreArrIndex] = scoreCashResult;
            }

            // 处理平+让球玩法
            if ("1".equals(winDrawLoseResult1)) {
                int handicapDrawArrIndex = getHandicapDrawArrIndex(1, Integer.parseInt(winDrawLoseResult2), "1".equals(winDrawLoseResult2));
                String handicapDrawCashResult = calculateCash(winDrawLoseOdds1, handicapOdds2);
                handicapDrawCash[handicapDrawArrIndex] = handicapDrawCashResult;
            }
            // 处理让球+平玩法
            if ("1".equals(winDrawLoseResult2)) {
                int handicapDrawArrIndex = getHandicapDrawArrIndex(Integer.parseInt(winDrawLoseResult1), 1, false);
                String handicapDrawCashResult = calculateCash(handicapOdds1, winDrawLoseOdds2);
                handicapDrawCash[handicapDrawArrIndex] = handicapDrawCashResult;
            }

            // 处理半全场玩法
            int handleHalfFullArrIndex = getHalfFullArrIndex(halfFullResult1, halfFullResult2);
            if (handleHalfFullArrIndex != -1) {
                String handleHalfFullCashResult = calculateCash(halfFullOdds1, halfFullOdds2);
                halfFullCash[handleHalfFullArrIndex] = handleHalfFullCashResult;
            }

            // 调用插入方法
            insertData(jcDate, type, num1, num2,
                    winLoseCash11,
                    handicapCash,
                    halfFullCash,
                    scoreCash,
                    handicapDrawCash);
        }
    }

    private static void insertData(String jcDate, BuyTableType type, String num1, String num2,
                              // 胜平负玩法
                              String winLoseCash11,

                              // 让球玩法参数数组
                              String[] handicapCashParams,

                              // 半全场玩法参数数组
                              String[] halfFullCashParams,

                              // 总进球数玩法参数数组
                              String[] scoreCashParams,

                              // 让球+平玩法参数数组
                              String[] handicapDrawCashParams) {

        boolean isExist = dao.searchBuyResult(type, jcDate);
        if (!isExist) {
            // 表中不存在该数据,应该插入到表中
            boolean insertResult = BuyDataUtils.insertBuyResultTable(type, jcDate, num1, num2,
                    winLoseCash11,
                    handicapCashParams,
                    halfFullCashParams,
                    scoreCashParams,
                    handicapDrawCashParams
            );
            if (!insertResult) {
                System.out.println("插入表失败");
            }
        } else {
            System.out.println("表中已存在对应数据");
        }
    }

    // 工具方法：初始化奖金数组
    private static void initializeCashArrays(String[] scoreCash, String[] handicapCash,
                                             String[] halfFullCash, String[] handicapDrawCash) {
        // 初始化总进球数数组
        for (int i = 0; i < 25; i++) {
            scoreCash[i] = "-100";
        }

        // 初始化让球数组
        for (int i = 0; i < 9; i++) {
            handicapCash[i] = "-100";
        }

        // 初始化让球+平数组
        for (int i = 0; i < 6; i++) {
            handicapDrawCash[i] = "-100";
        }

        // 初始化半全场数组
        for (int i = 0; i < 49; i++) {
            halfFullCash[i] = "-100";
        }
    }

    // 工具方法：计算奖金
    private static String calculateCash(String odds1, String odds2) {
        try {
            double cash = 100 * (Double.parseDouble(odds1) * Double.parseDouble(odds2));
            return String.valueOf((int)cash);
        } catch (NumberFormatException e) {
            System.out.println(e.toString());
            return "-100";
        }
    }

    private static int getHandicapArrIndex(int handicapResult1, int handicapResult2) {
        if (handicapResult1 == 3 && handicapResult2 == 3) {
            return  0;
        } else if (handicapResult1 == 3 && handicapResult2 == 1) {
            return  1;
        } else if (handicapResult1 == 3 && handicapResult2 == 0) {
            return  2;
        } else if (handicapResult1 == 1 && handicapResult2 == 3) {
            return  3;
        } else if (handicapResult1 == 1 && handicapResult2 == 1) {
            return  4;
        } else if (handicapResult1 == 1 && handicapResult2 == 0) {
            return  5;
        } else if (handicapResult1 == 0 && handicapResult2 == 3) {
            return  6;
        } else if (handicapResult1 == 0 && handicapResult2 == 1) {
            return  7;
        } else if (handicapResult1 == 0 && handicapResult2 == 0) {
            return  8;
        }
        return -1;
    }

    private static int getHandicapDrawArrIndex(int result1, int result2, boolean isSpecial) {
        if (result1 == 3 && result2 == 1) {
            return  0;
        } else if (result1 == 1 && result2 == 1 && !isSpecial) {
            return  1;
        } else if (result1 == 0 && result2 == 1) {
            return  2;
        } else if (result1 == 1 && result2 == 3) {
            return  3;
        } else if (result1 == 1 && result2 == 1 && isSpecial) {
            return  4;
        } else if (result1 == 1 && result2 == 0) {
            return  5;
        }
        return -1;
    }

    private static int getScoreArrIndex(int scoreSum1, int scoreSum2) {
        // 验证输入参数范围（0-4）
        if (scoreSum1 < 0 || scoreSum1 > 4 || scoreSum2 < 0 || scoreSum2 > 4) {
            return -1;
        }

        // 按照scoreCash参数顺序计算索引
        // 顺序是: 00, 01, 02, 03, 04, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 30, 31, 32, 33, 34, 40, 41, 42, 43, 44
        // 对应索引: 0,  1,  2,  3,  4,  5,  6,  7,  8,  9,  10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24

        return scoreSum1 * 5 + scoreSum2;
    }

    private static int getHalfFullArrIndex(String halfFullResult1, String halfFullResult2) {
        // 将半全场结果格式化为4位数字字符串（例如 "3-3" 变为 "33"）
        String result1 = halfFullResult1.replaceAll("-", "");
        String result2 = halfFullResult2.replaceAll("-", "");

        // 组合成4位字符串作为键值查找索引
        String key = result1 + result2;

        // 从映射表中获取索引
        if (halfFullIndexMap.containsKey(key)) {
            return halfFullIndexMap.get(key);
        }

        // 如果找不到对应键值，返回-1表示无效索引
        return -1;
    }
}
