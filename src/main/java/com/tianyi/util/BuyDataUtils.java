package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.dao.BuyTableType;

/**
 * 购买数据工具类
 * 提供便捷的购买记录操作方法，参照JCDataUtils格式
 */
public class BuyDataUtils {

    private static final BuyTableDAO dao = new BuyTableDAO();
    public static boolean insertBuyResultData(BuyTableType type, String jcTime, String fNum, String sNum,
                                              // 胜平负玩法
                                              String winLoseCash33, String winLoseOdds33,
                                              String winLoseCash31, String winLoseOdds31,
                                              String winLoseCash30, String winLoseOdds30,
                                              String winLoseCash13, String winLoseOdds13,
                                              String winLoseCash11, String winLoseOdds11,
                                              String winLoseCash10, String winLoseOdds10,
                                              String winLoseCash03, String winLoseOdds03,
                                              String winLoseCash01, String winLoseOdds01,
                                              String winLoseCash00, String winLoseOdds00,

                                              // 让球玩法
                                              String handicapCash33, String handicapOdds33,
                                              String handicapCash31, String handicapOdds31,
                                              String handicapCash30, String handicapOdds30,
                                              String handicapCash13, String handicapOdds13,
                                              String handicapCash11, String handicapOdds11,
                                              String handicapCash10, String handicapOdds10,
                                              String handicapCash03, String handicapOdds03,
                                              String handicapCash01, String handicapOdds01,
                                              String handicapCash00, String handicapOdds00,

                                              // 半全场玩法
                                              String halfFullCash3333, String halfFullOdds3333,
                                              String halfFullCash3331, String halfFullOdds3331,
                                              String halfFullCash3313, String halfFullOdds3313,
                                              String halfFullCash3311, String halfFullOdds3311,
                                              String halfFullCash3310, String halfFullOdds3310,
                                              String halfFullCash3301, String halfFullOdds3301,
                                              String halfFullCash3300, String halfFullOdds3300,

                                              String halfFullCash3133, String halfFullOdds3133,
                                              String halfFullCash3131, String halfFullOdds3131,
                                              String halfFullCash3113, String halfFullOdds3113,
                                              String halfFullCash3111, String halfFullOdds3111,
                                              String halfFullCash3110, String halfFullOdds3110,
                                              String halfFullCash3101, String halfFullOdds3101,
                                              String halfFullCash3100, String halfFullOdds3100,

                                              String halfFullCash1333, String halfFullOdds1333,
                                              String halfFullCash1331, String halfFullOdds1331,
                                              String halfFullCash1313, String halfFullOdds1313,
                                              String halfFullCash1311, String halfFullOdds1311,
                                              String halfFullCash1310, String halfFullOdds1310,
                                              String halfFullCash1301, String halfFullOdds1301,
                                              String halfFullCash1300, String halfFullOdds1300,

                                              String halfFullCash1133, String halfFullOdds1133,
                                              String halfFullCash1131, String halfFullOdds1131,
                                              String halfFullCash1113, String halfFullOdds1113,
                                              String halfFullCash1111, String halfFullOdds1111,
                                              String halfFullCash1110, String halfFullOdds1110,
                                              String halfFullCash1101, String halfFullOdds1101,
                                              String halfFullCash1100, String halfFullOdds1100,

                                              String halfFullCash1033, String halfFullOdds1033,
                                              String halfFullCash1031, String halfFullOdds1031,
                                              String halfFullCash1013, String halfFullOdds1013,
                                              String halfFullCash1011, String halfFullOdds1011,
                                              String halfFullCash1010, String halfFullOdds1010,
                                              String halfFullCash1001, String halfFullOdds1001,
                                              String halfFullCash1000, String halfFullOdds1000,

                                              String halfFullCash0133, String halfFullOdds0133,
                                              String halfFullCash0131, String halfFullOdds0131,
                                              String halfFullCash0113, String halfFullOdds0113,
                                              String halfFullCash0111, String halfFullOdds0111,
                                              String halfFullCash0110, String halfFullOdds0110,
                                              String halfFullCash0101, String halfFullOdds0101,
                                              String halfFullCash0100, String halfFullOdds0100,

                                              String halfFullCash0033, String halfFullOdds0033,
                                              String halfFullCash0031, String halfFullOdds0031,
                                              String halfFullCash0013, String halfFullOdds0013,
                                              String halfFullCash0011, String halfFullOdds0011,
                                              String halfFullCash0010, String halfFullOdds0010,
                                              String halfFullCash0001, String halfFullOdds0001,
                                              String halfFullCash0000, String halfFullOdds0000,

                                              // 总进球数玩法
                                              String scoreCash11, String scoreOdds11,
                                              String scoreCash12, String scoreOdds12,
                                              String scoreCash13, String scoreOdds13,
                                              String scoreCash14, String scoreOdds14,
                                              String scoreCash21, String scoreOdds21,
                                              String scoreCash22, String scoreOdds22,
                                              String scoreCash23, String scoreOdds23,
                                              String scoreCash24, String scoreOdds24,
                                              String scoreCash31, String scoreOdds31,
                                              String scoreCash32, String scoreOdds32,
                                              String scoreCash33, String scoreOdds33,
                                              String scoreCash34, String scoreOdds34,
                                              String scoreCash41, String scoreOdds41,
                                              String scoreCash42, String scoreOdds42,
                                              String scoreCash43, String scoreOdds43,
                                              String scoreCash44, String scoreOdds44,

                                              // 让球+平玩法
                                              String handicapDrawCash31, String handicapDrawOdds31,
                                              String handicapDrawCash101, String handicapDrawOdds101,
                                              String handicapDrawCash01, String handicapDrawOdds01,
                                              String handicapDrawCash13, String handicapDrawOdds13,
                                              String handicapDrawCash011, String handicapDrawOdds011,
                                              String handicapDrawCash10, String handicapDrawOdds10,

                                              // 让球+平玩法(特殊)
                                              String handicapDrawSCash31, String handicapDrawSOdds31,
                                              String handicapDrawSCash101, String handicapDrawSOdds101,
                                              String handicapDrawSCash01, String handicapDrawSOdds01,
                                              String handicapDrawSCash13, String handicapDrawSOdds13,
                                              String handicapDrawSCash011, String handicapDrawSOdds011,
                                              String handicapDrawSCash10, String handicapDrawSOdds10)  {

        return dao.insertBuyResult(type, jcTime, fNum, sNum, winLoseCash33,);
    }
}
