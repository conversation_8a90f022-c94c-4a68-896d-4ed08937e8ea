package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.dao.BuyTableType;

/**
 * 购买数据工具类
 * 提供便捷的购买记录操作方法，参照JCDataUtils格式
 */
public class BuyDataUtils {

    private static final BuyTableDAO dao = new BuyTableDAO();
    public static boolean insertBuyResultData(BuyTableType type, String jcTime, String fNum, String sNum,
                                              // 胜平负玩法
                                              String winLoseCash11,

                                              // 让球玩法
                                              String handicapCash33,
                                              String handicapCash31,
                                              String handicapCash30,
                                              String handicapCash13,
                                              String handicapCash11,
                                              String handicapCash10,
                                              String handicapCash03,
                                              String handicapCash01,
                                              String handicapCash00,

                                              // 半全场玩法
                                              String halfFullCash3333,
                                              String halfFullCash3331,
                                              String halfFullCash3313,
                                              String halfFullCash3311,
                                              String halfFullCash3310,
                                              String halfFullCash3301,
                                              String halfFullCash3300,

                                              String halfFullCash3133,
                                              String halfFullCash3131,
                                              String halfFullCash3113,
                                              String halfFullCash3111,
                                              String halfFullCash3110,
                                              String halfFullCash3101,
                                              String halfFullCash3100,

                                              String halfFullCash1333,
                                              String halfFullCash1331,
                                              String halfFullCash1313,
                                              String halfFullCash1311,
                                              String halfFullCash1310,
                                              String halfFullCash1301,
                                              String halfFullCash1300,

                                              String halfFullCash1133,
                                              String halfFullCash1131,
                                              String halfFullCash1113,
                                              String halfFullCash1111,
                                              String halfFullCash1110,
                                              String halfFullCash1101,
                                              String halfFullCash1100,

                                              String halfFullCash1033,
                                              String halfFullCash1031,
                                              String halfFullCash1013,
                                              String halfFullCash1011,
                                              String halfFullCash1010,
                                              String halfFullCash1001,
                                              String halfFullCash1000,

                                              String halfFullCash0133,
                                              String halfFullCash0131,
                                              String halfFullCash0113,
                                              String halfFullCash0111,
                                              String halfFullCash0110,
                                              String halfFullCash0101,
                                              String halfFullCash0100,

                                              String halfFullCash0033,
                                              String halfFullCash0031,
                                              String halfFullCash0013,
                                              String halfFullCash0011,
                                              String halfFullCash0010,
                                              String halfFullCash0001,
                                              String halfFullCash0000,

                                              // 总进球数玩法
                                              String scoreCash00,
                                              String scoreCash01,
                                              String scoreCash02,
                                              String scoreCash03,
                                              String scoreCash04,
                                              String scoreCash10,
                                              String scoreCash11,
                                              String scoreCash12,
                                              String scoreCash13,
                                              String scoreCash14,
                                              String scoreCash20,
                                              String scoreCash21,
                                              String scoreCash22,
                                              String scoreCash23,
                                              String scoreCash24,
                                              String scoreCash30,
                                              String scoreCash31,
                                              String scoreCash32,
                                              String scoreCash33,
                                              String scoreCash34,
                                              String scoreCash40,
                                              String scoreCash41,
                                              String scoreCash42,
                                              String scoreCash43,
                                              String scoreCash44,

                                              // 让球+平玩法
                                              String handicapDrawCash31,
                                              String handicapDrawCash101,
                                              String handicapDrawCash01,
                                              String handicapDrawCash13,
                                              String handicapDrawCash011,
                                              String handicapDrawCash10)  {

        return dao.insertBuyResult(type, jcTime, fNum, sNum,
            // 胜平负玩法参数
            winLoseCash11,

            // 让球玩法参数
            handicapCash33,
            handicapCash31,
            handicapCash30,
            handicapCash13,
            handicapCash11,
            handicapCash10,
            handicapCash03,
            handicapCash01,
            handicapCash00,

            // 半全场玩法参数 - 第一组 (33-xx)
            halfFullCash3333,
            halfFullCash3331,
            halfFullCash3313,
            halfFullCash3311,
            halfFullCash3310,
            halfFullCash3301, halfFullOdds3301,
            halfFullCash3300, halfFullOdds3300,

            // 半全场玩法参数 - 第二组 (31-xx)
            halfFullCash3133, halfFullOdds3133,
            halfFullCash3131, halfFullOdds3131,
            halfFullCash3113, halfFullOdds3113,
            halfFullCash3111, halfFullOdds3111,
            halfFullCash3110, halfFullOdds3110,
            halfFullCash3101, halfFullOdds3101,
            halfFullCash3100, halfFullOdds3100,

            // 半全场玩法参数 - 第三组 (13-xx)
            halfFullCash1333, halfFullOdds1333,
            halfFullCash1331, halfFullOdds1331,
            halfFullCash1313, halfFullOdds1313,
            halfFullCash1311, halfFullOdds1311,
            halfFullCash1310, halfFullOdds1310,
            halfFullCash1301, halfFullOdds1301,
            halfFullCash1300, halfFullOdds1300,

            // 半全场玩法参数 - 第四组 (11-xx)
            halfFullCash1133, halfFullOdds1133,
            halfFullCash1131, halfFullOdds1131,
            halfFullCash1113, halfFullOdds1113,
            halfFullCash1111, halfFullOdds1111,
            halfFullCash1110, halfFullOdds1110,
            halfFullCash1101, halfFullOdds1101,
            halfFullCash1100, halfFullOdds1100,

            // 半全场玩法参数 - 第五组 (10-xx)
            halfFullCash1033, halfFullOdds1033,
            halfFullCash1031, halfFullOdds1031,
            halfFullCash1013, halfFullOdds1013,
            halfFullCash1011, halfFullOdds1011,
            halfFullCash1010, halfFullOdds1010,
            halfFullCash1001, halfFullOdds1001,
            halfFullCash1000, halfFullOdds1000,

            // 半全场玩法参数 - 第六组 (01-xx)
            halfFullCash0133, halfFullOdds0133,
            halfFullCash0131, halfFullOdds0131,
            halfFullCash0113, halfFullOdds0113,
            halfFullCash0111, halfFullOdds0111,
            halfFullCash0110, halfFullOdds0110,
            halfFullCash0101, halfFullOdds0101,
            halfFullCash0100, halfFullOdds0100,

            // 半全场玩法参数 - 第七组 (00-xx)
            halfFullCash0033, halfFullOdds0033,
            halfFullCash0031, halfFullOdds0031,
            halfFullCash0013, halfFullOdds0013,
            halfFullCash0011, halfFullOdds0011,
            halfFullCash0010, halfFullOdds0010,
            halfFullCash0001, halfFullOdds0001,
            halfFullCash0000, halfFullOdds0000,

            // 总进球数玩法参数
            scoreCash11, scoreOdds11,
            scoreCash12, scoreOdds12,
            scoreCash13, scoreOdds13,
            scoreCash14, scoreOdds14,
            scoreCash21, scoreOdds21,
            scoreCash22, scoreOdds22,
            scoreCash23, scoreOdds23,
            scoreCash24, scoreOdds24,
            scoreCash31, scoreOdds31,
            scoreCash32, scoreOdds32,
            scoreCash33, scoreOdds33,
            scoreCash34, scoreOdds34,
            scoreCash41, scoreOdds41,
            scoreCash42, scoreOdds42,
            scoreCash43, scoreOdds43,
            scoreCash44, scoreOdds44,

            // 让球+平玩法参数
            handicapDrawCash31, handicapDrawOdds31,
            handicapDrawCash101, handicapDrawOdds101,
            handicapDrawCash01, handicapDrawOdds01,
            handicapDrawCash13, handicapDrawOdds13,
            handicapDrawCash011, handicapDrawOdds011,
            handicapDrawCash10, handicapDrawOdds10
        );
    }
}
