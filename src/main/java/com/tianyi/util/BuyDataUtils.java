package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 购买数据工具类
 * 提供便捷的购买记录操作方法
 */
public class BuyDataUtils {
    
    private static final BuyTableDAO dao = new BuyTableDAO();
    
    /**
     * 插入购买记录
     * 
     * @param userId 用户ID
     * @param matchId 比赛ID
     * @param num 场次编号
     * @param jcTime 竞彩时间
     * @param matchTime 比赛时间
     * @param league 联赛名称
     * @param home 主队名称
     * @param away 客队名称
     * @param betType 投注类型(胜平负/让球/比分/总进球/半全场)
     * @param betOption 投注选项
     * @param betOdds 投注赔率
     * @param betAmount 投注金额
     * @param notes 备注
     * @return 是否插入成功
     */
    public static boolean insertBuyRecord(String userId, String matchId, String num,
                                         String jcTime, String matchTime, String league,
                                         String home, String away, String betType,
                                         String betOption, String betOdds,
                                         BigDecimal betAmount, String notes) {
        
        // 计算预期收益
        BigDecimal potentialReturn = calculatePotentialReturn(betAmount, betOdds);
        
        return dao.insertBuyRecord(userId, matchId, num, jcTime, matchTime, league,
                                  home, away, betType, betOption, betOdds,
                                  betAmount, potentialReturn, notes);
    }
    
    /**
     * 插入购买记录（简化版本，只需要核心参数）
     */
    public static boolean insertSimpleBuyRecord(String userId, String matchId, String num,
                                               String betType, String betOption, 
                                               String betOdds, BigDecimal betAmount) {
        
        return insertBuyRecord(userId, matchId, num, null, null, null,
                              null, null, betType, betOption, betOdds,
                              betAmount, null);
    }
    
    /**
     * 批量插入购买记录示例数据
     */
    public static void insertSampleBuyRecords() {
        System.out.println("正在插入示例购买记录...");
        
        // 示例数据1：胜平负投注
        boolean result1 = insertBuyRecord(
            "user001", "1282037", "周六018",
            "2025-01-01 12:00", "01-01 00:45", "挪超",
            "特罗姆瑟", "瓦勒伦加", "胜平负", "主胜", "1.700",
            new BigDecimal("100.00"), "看好主队"
        );
        
        // 示例数据2：让球投注
        boolean result2 = insertBuyRecord(
            "user002", "1282038", "周六019",
            "2025-01-01 15:00", "01-01 03:00", "英超",
            "曼联", "利物浦", "让球", "主队-1", "3.300",
            new BigDecimal("50.00"), "曼联让球优势"
        );
        
        // 示例数据3：比分投注
        boolean result3 = insertBuyRecord(
            "user001", "1282039", "周六020",
            "2025-01-02 09:00", "01-02 21:00", "西甲",
            "皇马", "巴萨", "比分", "2:1", "7.000",
            new BigDecimal("20.00"), "经典比分"
        );
        
        // 示例数据4：总进球投注
        boolean result4 = insertBuyRecord(
            "user003", "1282040", "周日001",
            "2025-01-02 14:30", "01-02 02:30", "德甲",
            "拜仁", "多特", "总进球", "3球", "3.500",
            new BigDecimal("80.00"), "进球大战"
        );
        
        int successCount = 0;
        if (result1) successCount++;
        if (result2) successCount++;
        if (result3) successCount++;
        if (result4) successCount++;
        
        System.out.println("✅ 示例数据插入完成，成功插入 " + successCount + " 条记录");
    }
    
    /**
     * 查询用户的所有购买记录
     */
    public static void showUserBuyRecords(String userId) {
        dao.getBuyRecordsByUserId(userId);
    }
    
    /**
     * 查询比赛的所有购买记录
     */
    public static void showMatchBuyRecords(String matchId) {
        dao.getBuyRecordsByMatchId(matchId);
    }
    
    /**
     * 查询所有购买记录
     */
    public static void showAllBuyRecords() {
        dao.getAllBuyRecords();
    }
    
    /**
     * 更新购买记录结果
     */
    public static boolean updateBuyRecordResult(int recordId, String matchResult,
                                               String betResult, BigDecimal actualReturn) {
        
        // 计算盈亏
        BigDecimal profitLoss = actualReturn.subtract(getBetAmountByRecordId(recordId));
        
        // 确定状态
        String status = "WON".equals(betResult) ? "WON" : "LOST";
        
        return dao.updateBuyRecordResult(recordId, matchResult, betResult,
                                        actualReturn, profitLoss, status);
    }
    
    /**
     * 删除购买记录
     */
    public static boolean deleteBuyRecord(int recordId) {
        return dao.deleteBuyRecord(recordId);
    }
    
    /**
     * 获取购买记录总数
     */
    public static int getBuyRecordCount() {
        return dao.getBuyRecordCount();
    }
    
    /**
     * 获取用户统计信息
     */
    public static void showUserStatistics(String userId) {
        System.out.println("=== 用户 " + userId + " 统计信息 ===");
        
        BigDecimal totalBetAmount = dao.getUserTotalBetAmount(userId);
        BigDecimal totalProfitLoss = dao.getUserTotalProfitLoss(userId);
        
        System.out.println("总投注金额: ¥" + totalBetAmount);
        System.out.println("总盈亏金额: ¥" + totalProfitLoss);
        
        if (totalBetAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal winRate = totalProfitLoss.divide(totalBetAmount, 4, RoundingMode.HALF_UP)
                                               .multiply(new BigDecimal("100"));
            System.out.println("盈亏率: " + winRate + "%");
        }
        
        System.out.println();
    }
    
    /**
     * 计算预期收益
     */
    private static BigDecimal calculatePotentialReturn(BigDecimal betAmount, String betOdds) {
        try {
            BigDecimal odds = new BigDecimal(betOdds);
            return betAmount.multiply(odds).setScale(2, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            System.err.println("赔率格式错误: " + betOdds);
            return betAmount;
        }
    }
    
    /**
     * 根据记录ID获取投注金额（辅助方法）
     */
    private static BigDecimal getBetAmountByRecordId(int recordId) {
        // 这里应该查询数据库获取投注金额，简化处理返回0
        // 实际应用中需要实现具体的查询逻辑
        return BigDecimal.ZERO;
    }
    
    /**
     * 投注类型枚举
     */
    public enum BetType {
        WIN_DRAW_LOSE("胜平负"),
        HANDICAP("让球"),
        SCORE("比分"),
        TOTAL_GOALS("总进球"),
        HALF_FULL("半全场");
        
        private final String description;
        
        BetType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 投注状态枚举
     */
    public enum BetStatus {
        PENDING("待开奖"),
        WON("中奖"),
        LOST("未中奖"),
        CANCELLED("已取消");
        
        private final String description;
        
        BetStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 便捷的投注方法 - 胜平负
     */
    public static boolean betWinDrawLose(String userId, String matchId, String num,
                                        String option, String odds, BigDecimal amount) {
        return insertSimpleBuyRecord(userId, matchId, num, 
                                   BetType.WIN_DRAW_LOSE.getDescription(), 
                                   option, odds, amount);
    }
    
    /**
     * 便捷的投注方法 - 让球
     */
    public static boolean betHandicap(String userId, String matchId, String num,
                                     String handicap, String odds, BigDecimal amount) {
        return insertSimpleBuyRecord(userId, matchId, num, 
                                   BetType.HANDICAP.getDescription(), 
                                   handicap, odds, amount);
    }
    
    /**
     * 便捷的投注方法 - 比分
     */
    public static boolean betScore(String userId, String matchId, String num,
                                  String score, String odds, BigDecimal amount) {
        return insertSimpleBuyRecord(userId, matchId, num, 
                                   BetType.SCORE.getDescription(), 
                                   score, odds, amount);
    }
    
    /**
     * 便捷的投注方法 - 总进球
     */
    public static boolean betTotalGoals(String userId, String matchId, String num,
                                       String goals, String odds, BigDecimal amount) {
        return insertSimpleBuyRecord(userId, matchId, num, 
                                   BetType.TOTAL_GOALS.getDescription(), 
                                   goals, odds, amount);
    }
    
    /**
     * 便捷的投注方法 - 半全场
     */
    public static boolean betHalfFull(String userId, String matchId, String num,
                                     String halfFull, String odds, BigDecimal amount) {
        return insertSimpleBuyRecord(userId, matchId, num, 
                                   BetType.HALF_FULL.getDescription(), 
                                   halfFull, odds, amount);
    }
}
