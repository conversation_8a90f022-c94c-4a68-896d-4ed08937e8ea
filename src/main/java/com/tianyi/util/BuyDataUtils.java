package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.dao.BuyTableType;
import com.tianyi.model.MatchData;

import java.util.List;

/**
 * 购买数据工具类
 * 提供便捷的购买记录操作方法，参照JCDataUtils格式
 */
public class BuyDataUtils {

    private static final BuyTableDAO dao = new BuyTableDAO();
    public static boolean insertBuyResultTable(BuyTableType type, String jcTime, String fNum, String sNum,
                                              // 胜平负玩法
                                              String winLoseCash33, String winLoseOdds33,
                                              String winLoseCash31, String winLoseOdds31,
                                              String winLoseCash30, String winLoseOdds30,
                                              String winLoseCash13, String winLoseOdds13,
                                              String winLoseCash11, String winLoseOdds11,
                                              String winLoseCash10, String winLoseOdds10,
                                              String winLoseCash03, String winLoseOdds03,
                                              String winLoseCash01, String winLoseOdds01,
                                              String winLoseCash00, String winLoseOdds00,

                                              // 让球玩法
                                              String handicapCash33, String handicapOdds33,
                                              String handicapCash31, String handicapOdds31,
                                              String handicapCash30, String handicapOdds30,
                                              String handicapCash13, String handicapOdds13,
                                              String handicapCash11, String handicapOdds11,
                                              String handicapCash10, String handicapOdds10,
                                              String handicapCash03, String handicapOdds03,
                                              String handicapCash01, String handicapOdds01,
                                              String handicapCash00, String handicapOdds00,

                                              // 半全场玩法
                                              String halfFullCash3333, String halfFullOdds3333,
                                              String halfFullCash3331, String halfFullOdds3331,
                                              String halfFullCash3313, String halfFullOdds3313,
                                              String halfFullCash3311, String halfFullOdds3311,
                                              String halfFullCash3310, String halfFullOdds3310,
                                              String halfFullCash3301, String halfFullOdds3301,
                                              String halfFullCash3300, String halfFullOdds3300,

                                              String halfFullCash3133, String halfFullOdds3133,
                                              String halfFullCash3131, String halfFullOdds3131,
                                              String halfFullCash3113, String halfFullOdds3113,
                                              String halfFullCash3111, String halfFullOdds3111,
                                              String halfFullCash3110, String halfFullOdds3110,
                                              String halfFullCash3101, String halfFullOdds3101,
                                              String halfFullCash3100, String halfFullOdds3100,

                                              String halfFullCash1333, String halfFullOdds1333,
                                              String halfFullCash1331, String halfFullOdds1331,
                                              String halfFullCash1313, String halfFullOdds1313,
                                              String halfFullCash1311, String halfFullOdds1311,
                                              String halfFullCash1310, String halfFullOdds1310,
                                              String halfFullCash1301, String halfFullOdds1301,
                                              String halfFullCash1300, String halfFullOdds1300,

                                              String halfFullCash1133, String halfFullOdds1133,
                                              String halfFullCash1131, String halfFullOdds1131,
                                              String halfFullCash1113, String halfFullOdds1113,
                                              String halfFullCash1111, String halfFullOdds1111,
                                              String halfFullCash1110, String halfFullOdds1110,
                                              String halfFullCash1101, String halfFullOdds1101,
                                              String halfFullCash1100, String halfFullOdds1100,

                                              String halfFullCash1033, String halfFullOdds1033,
                                              String halfFullCash1031, String halfFullOdds1031,
                                              String halfFullCash1013, String halfFullOdds1013,
                                              String halfFullCash1011, String halfFullOdds1011,
                                              String halfFullCash1010, String halfFullOdds1010,
                                              String halfFullCash1001, String halfFullOdds1001,
                                              String halfFullCash1000, String halfFullOdds1000,

                                              String halfFullCash0133, String halfFullOdds0133,
                                              String halfFullCash0131, String halfFullOdds0131,
                                              String halfFullCash0113, String halfFullOdds0113,
                                              String halfFullCash0111, String halfFullOdds0111,
                                              String halfFullCash0110, String halfFullOdds0110,
                                              String halfFullCash0101, String halfFullOdds0101,
                                              String halfFullCash0100, String halfFullOdds0100,

                                              String halfFullCash0033, String halfFullOdds0033,
                                              String halfFullCash0031, String halfFullOdds0031,
                                              String halfFullCash0013, String halfFullOdds0013,
                                              String halfFullCash0011, String halfFullOdds0011,
                                              String halfFullCash0010, String halfFullOdds0010,
                                              String halfFullCash0001, String halfFullOdds0001,
                                              String halfFullCash0000, String halfFullOdds0000,

                                              // 总进球数玩法
                                              String scoreCash11, String scoreOdds11,
                                              String scoreCash12, String scoreOdds12,
                                              String scoreCash13, String scoreOdds13,
                                              String scoreCash14, String scoreOdds14,
                                              String scoreCash21, String scoreOdds21,
                                              String scoreCash22, String scoreOdds22,
                                              String scoreCash23, String scoreOdds23,
                                              String scoreCash24, String scoreOdds24,
                                              String scoreCash31, String scoreOdds31,
                                              String scoreCash32, String scoreOdds32,
                                              String scoreCash33, String scoreOdds33,
                                              String scoreCash34, String scoreOdds34,
                                              String scoreCash41, String scoreOdds41,
                                              String scoreCash42, String scoreOdds42,
                                              String scoreCash43, String scoreOdds43,
                                              String scoreCash44, String scoreOdds44,

                                              // 让球+平玩法
                                              String handicapDrawCash31, String handicapDrawOdds31,
                                              String handicapDrawCash101, String handicapDrawOdds101,
                                              String handicapDrawCash01, String handicapDrawOdds01,
                                              String handicapDrawCash13, String handicapDrawOdds13,
                                              String handicapDrawCash011, String handicapDrawOdds011,
                                              String handicapDrawCash10, String handicapDrawOdds10,

                                              // 让球+平玩法(特殊)
                                              String handicapDrawSCash31, String handicapDrawSOdds31,
                                              String handicapDrawSCash101, String handicapDrawSOdds101,
                                              String handicapDrawSCash01, String handicapDrawSOdds01,
                                              String handicapDrawSCash13, String handicapDrawSOdds13,
                                              String handicapDrawSCash011, String handicapDrawSOdds011,
                                              String handicapDrawSCash10, String handicapDrawSOdds10)  {

        return dao.insertBuyResult(type, jcTime, fNum, sNum,
            // 胜平负玩法参数
            winLoseCash33, winLoseOdds33,
            winLoseCash31, winLoseOdds31,
            winLoseCash30, winLoseOdds30,
            winLoseCash13, winLoseOdds13,
            winLoseCash11, winLoseOdds11,
            winLoseCash10, winLoseOdds10,
            winLoseCash03, winLoseOdds03,
            winLoseCash01, winLoseOdds01,
            winLoseCash00, winLoseOdds00,

            // 让球玩法参数
            handicapCash33, handicapOdds33,
            handicapCash31, handicapOdds31,
            handicapCash30, handicapOdds30,
            handicapCash13, handicapOdds13,
            handicapCash11, handicapOdds11,
            handicapCash10, handicapOdds10,
            handicapCash03, handicapOdds03,
            handicapCash01, handicapOdds01,
            handicapCash00, handicapOdds00,

            // 半全场玩法参数 - 第一组 (33-xx)
            halfFullCash3333, halfFullOdds3333,
            halfFullCash3331, halfFullOdds3331,
            halfFullCash3313, halfFullOdds3313,
            halfFullCash3311, halfFullOdds3311,
            halfFullCash3310, halfFullOdds3310,
            halfFullCash3301, halfFullOdds3301,
            halfFullCash3300, halfFullOdds3300,

            // 半全场玩法参数 - 第二组 (31-xx)
            halfFullCash3133, halfFullOdds3133,
            halfFullCash3131, halfFullOdds3131,
            halfFullCash3113, halfFullOdds3113,
            halfFullCash3111, halfFullOdds3111,
            halfFullCash3110, halfFullOdds3110,
            halfFullCash3101, halfFullOdds3101,
            halfFullCash3100, halfFullOdds3100,

            // 半全场玩法参数 - 第三组 (13-xx)
            halfFullCash1333, halfFullOdds1333,
            halfFullCash1331, halfFullOdds1331,
            halfFullCash1313, halfFullOdds1313,
            halfFullCash1311, halfFullOdds1311,
            halfFullCash1310, halfFullOdds1310,
            halfFullCash1301, halfFullOdds1301,
            halfFullCash1300, halfFullOdds1300,

            // 半全场玩法参数 - 第四组 (11-xx)
            halfFullCash1133, halfFullOdds1133,
            halfFullCash1131, halfFullOdds1131,
            halfFullCash1113, halfFullOdds1113,
            halfFullCash1111, halfFullOdds1111,
            halfFullCash1110, halfFullOdds1110,
            halfFullCash1101, halfFullOdds1101,
            halfFullCash1100, halfFullOdds1100,

            // 半全场玩法参数 - 第五组 (10-xx)
            halfFullCash1033, halfFullOdds1033,
            halfFullCash1031, halfFullOdds1031,
            halfFullCash1013, halfFullOdds1013,
            halfFullCash1011, halfFullOdds1011,
            halfFullCash1010, halfFullOdds1010,
            halfFullCash1001, halfFullOdds1001,
            halfFullCash1000, halfFullOdds1000,

            // 半全场玩法参数 - 第六组 (01-xx)
            halfFullCash0133, halfFullOdds0133,
            halfFullCash0131, halfFullOdds0131,
            halfFullCash0113, halfFullOdds0113,
            halfFullCash0111, halfFullOdds0111,
            halfFullCash0110, halfFullOdds0110,
            halfFullCash0101, halfFullOdds0101,
            halfFullCash0100, halfFullOdds0100,

            // 半全场玩法参数 - 第七组 (00-xx)
            halfFullCash0033, halfFullOdds0033,
            halfFullCash0031, halfFullOdds0031,
            halfFullCash0013, halfFullOdds0013,
            halfFullCash0011, halfFullOdds0011,
            halfFullCash0010, halfFullOdds0010,
            halfFullCash0001, halfFullOdds0001,
            halfFullCash0000, halfFullOdds0000,

            // 总进球数玩法参数
            scoreCash11, scoreOdds11,
            scoreCash12, scoreOdds12,
            scoreCash13, scoreOdds13,
            scoreCash14, scoreOdds14,
            scoreCash21, scoreOdds21,
            scoreCash22, scoreOdds22,
            scoreCash23, scoreOdds23,
            scoreCash24, scoreOdds24,
            scoreCash31, scoreOdds31,
            scoreCash32, scoreOdds32,
            scoreCash33, scoreOdds33,
            scoreCash34, scoreOdds34,
            scoreCash41, scoreOdds41,
            scoreCash42, scoreOdds42,
            scoreCash43, scoreOdds43,
            scoreCash44, scoreOdds44,

            // 让球+平玩法参数
            handicapDrawCash31, handicapDrawOdds31,
            handicapDrawCash101, handicapDrawOdds101,
            handicapDrawCash01, handicapDrawOdds01,
            handicapDrawCash13, handicapDrawOdds13,
            handicapDrawCash011, handicapDrawOdds011,
            handicapDrawCash10, handicapDrawOdds10,

            // 让球+平玩法(特殊)参数
            handicapDrawSCash31, handicapDrawSOdds31,
            handicapDrawSCash101, handicapDrawSOdds101,
            handicapDrawSCash01, handicapDrawSOdds01,
            handicapDrawSCash13, handicapDrawSOdds13,
            handicapDrawSCash011, handicapDrawSOdds011,
            handicapDrawSCash10, handicapDrawSOdds10
        );
    }

    public static void insertBuyResultData(BuyTableType type, MatchData match1, MatchData match2, String timeStr) {
        String jcDate = "";
        // 添加场次日期信息
        try {
            jcDate = TimeUtil.getJCDateFromWeekday(timeStr, match1.getMatchTime(), match1.getNum());
        } catch (Exception e) {
            System.out.println("场次日期: 无法转换 (" + e.getMessage() + ")");
        }
        String num1 = match1.getNum();
        String num2 = match2.getNum();
        String full1 = match1.getResult().getFull();
        String full2 = match2.getResult().getFull();
        if (!full1.isEmpty() && !full2.isEmpty()) {
            String winDrawLoseResult1 = match1.getResult().getWinDrawLoseResult();
            String winDrawLoseResult2 = match2.getResult().getWinDrawLoseResult();
            String winDrawLoseOdds1 = match1.getResult().getWinDrawLoseOdds();
            String winDrawLoseOdds2 = match2.getResult().getWinDrawLoseOdds();

            String handicapResult1 = match1.getResult().getHandicapResult();
            String handicapResult2 = match2.getResult().getHandicapResult();
            String handicapOdds1 = match1.getResult().getHandicapOdds();
            String handicapOdds2 = match2.getResult().getHandicapOdds();

//            String score1 =
            String[] scoreList1 = full1.split("-");
            String[] scoreList2 = full2.split("-");
            int scoreSum1 = Integer.parseInt(scoreList1[0]) + Integer.parseInt(scoreList1[1]);
            String scoreSumStr1 = String.valueOf(scoreSum1);
            int scoreSum2 = Integer.parseInt(scoreList2[0]) + Integer.parseInt(scoreList2[1]);
            String scoreSumStr2 = String.valueOf(scoreSum2);
            String scoreOdds1 = match1.getResult().getGoalOdds();
            String scoreOdds2 = match2.getResult().getGoalOdds();

            String halfFullResult1 = match1.getResult().getHalfFullResult();
            String halfFullResult2 = match2.getResult().getHalfFullResult();
            String halfFullOdds1 = match1.getResult().getHalfFullOdds();
            String halfFullOdds2 = match2.getResult().getHalfFullOdds();

            System.out.println("ssss");
//            BuyDataUtils.insertBuyResultTable(type, jcDate, num1, num2, );
        }
    }
}
