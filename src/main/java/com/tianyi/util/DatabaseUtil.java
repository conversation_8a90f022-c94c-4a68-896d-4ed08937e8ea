package com.tianyi.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库连接工具类
 * 用于管理MySQL数据库连接和基本操作
 */
public class DatabaseUtil {
    
    // 数据库连接配置
    private static final String DB_HOST = "localhost";
    private static final String DB_PORT = "3306";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "821661664"; // MySQL密码
    
    // 数据库名称
    private static final String DATABASE_NAME = "TianyiSport";
    
    // JDBC URL (不包含数据库名，用于创建数据库)
    private static final String JDBC_URL_WITHOUT_DB = 
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + 
        "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
    
    // JDBC URL (包含数据库名，用于连接到具体数据库)
    private static final String JDBC_URL_WITH_DB = 
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + "/" + DATABASE_NAME + 
        "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
    
    /**
     * 获取数据库连接（不指定数据库，用于创建数据库）
     */
    public static Connection getConnectionWithoutDB() throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            return DriverManager.getConnection(JDBC_URL_WITHOUT_DB, DB_USERNAME, DB_PASSWORD);
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL JDBC Driver not found", e);
        }
    }
    
    /**
     * 获取数据库连接（连接到TianyiSport数据库）
     */
    public static Connection getConnection() throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            return DriverManager.getConnection(JDBC_URL_WITH_DB, DB_USERNAME, DB_PASSWORD);
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL JDBC Driver not found", e);
        }
    }
    
    /**
     * 测试MySQL服务器连接
     */
    public static boolean testMySQLConnection() {
        Connection connection = null;

        try {
            connection = getConnectionWithoutDB();
            System.out.println("成功连接到MySQL服务器！");
            return true;
        } catch (SQLException e) {
            System.err.println("连接MySQL服务器失败: " + e.getMessage());
            System.err.println("请检查：");
            System.err.println("1. MySQL服务是否已启动");
            System.err.println("2. 用户名和密码是否正确");
            System.err.println("3. 端口3306是否可访问");
            return false;
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    System.err.println("关闭连接失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 创建TianyiSport数据库
     */
    public static boolean createDatabase() {
        Connection connection = null;
        Statement statement = null;

        try {
            // 先测试MySQL服务器连接
            if (!testMySQLConnection()) {
                return false;
            }

            // 连接到MySQL服务器（不指定数据库）
            connection = getConnectionWithoutDB();
            statement = connection.createStatement();

            // 创建数据库的SQL语句
            String createDatabaseSQL = "CREATE DATABASE IF NOT EXISTS " + DATABASE_NAME +
                                     " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

            // 执行创建数据库的SQL
            statement.executeUpdate(createDatabaseSQL);

            System.out.println("数据库 '" + DATABASE_NAME + "' 创建成功！");
            return true;

        } catch (SQLException e) {
            System.err.println("创建数据库失败: " + e.getMessage());

            // 提供更详细的错误信息
            if (e.getMessage().contains("Access denied")) {
                System.err.println("访问被拒绝，请检查MySQL用户权限");
                System.err.println("当前配置：用户名=" + DB_USERNAME + ", 密码=" + (DB_PASSWORD.isEmpty() ? "空" : "已设置"));
            }

            return false;
        } finally {
            // 关闭资源
            closeResources(connection, statement, null);
        }
    }
    
    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        Connection connection = null;
        
        try {
            connection = getConnection();
            System.out.println("成功连接到数据库 '" + DATABASE_NAME + "'！");
            return true;
        } catch (SQLException e) {
            System.err.println("连接数据库失败: " + e.getMessage());
            return false;
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    System.err.println("关闭连接失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 关闭数据库资源
     */
    public static void closeResources(Connection connection, Statement statement, 
                                    java.sql.ResultSet resultSet) {
        try {
            if (resultSet != null) resultSet.close();
            if (statement != null) statement.close();
            if (connection != null) connection.close();
        } catch (SQLException e) {
            System.err.println("关闭数据库资源失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取数据库名称
     */
    public static String getDatabaseName() {
        return DATABASE_NAME;
    }
}
