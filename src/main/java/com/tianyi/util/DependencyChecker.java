package com.tianyi.util;

/**
 * 依赖检查工具
 * 用于检查Jackson等关键依赖是否正确加载
 */
public class DependencyChecker {
    
    public static void main(String[] args) {
        System.out.println("=== 依赖检查工具 ===");
        System.out.println();
        
        checkJacksonDependencies();
        checkMySQLDependencies();
        
        System.out.println("=== 检查完成 ===");
    }
    
    /**
     * 检查Jackson依赖
     */
    public static void checkJacksonDependencies() {
        System.out.println("1. 检查Jackson依赖...");
        
        try {
            // 检查jackson-core
            Class.forName("com.fasterxml.jackson.core.JsonFactory");
            System.out.println("   ✓ jackson-core 加载成功");
        } catch (ClassNotFoundException e) {
            System.out.println("   ✗ jackson-core 未找到");
        }
        
        try {
            // 检查jackson-annotations
            Class.forName("com.fasterxml.jackson.annotation.JsonProperty");
            System.out.println("   ✓ jackson-annotations 加载成功");
        } catch (ClassNotFoundException e) {
            System.out.println("   ✗ jackson-annotations 未找到");
        }
        
        try {
            // 检查jackson-databind
            Class.forName("com.fasterxml.jackson.databind.ObjectMapper");
            System.out.println("   ✓ jackson-databind 加载成功");
        } catch (ClassNotFoundException e) {
            System.out.println("   ✗ jackson-databind 未找到");
        }
        
        // 测试ObjectMapper创建
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = 
                new com.fasterxml.jackson.databind.ObjectMapper();
            System.out.println("   ✓ ObjectMapper 创建成功");
        } catch (Exception e) {
            System.out.println("   ✗ ObjectMapper 创建失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 检查MySQL依赖
     */
    public static void checkMySQLDependencies() {
        System.out.println("2. 检查MySQL依赖...");
        
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("   ✓ MySQL JDBC驱动加载成功");
        } catch (ClassNotFoundException e) {
            System.out.println("   ✗ MySQL JDBC驱动未找到");
        }
        
        System.out.println();
    }
    
    /**
     * 获取类路径信息
     */
    public static void printClasspathInfo() {
        System.out.println("3. 类路径信息...");
        String classpath = System.getProperty("java.class.path");
        String[] paths = classpath.split(System.getProperty("path.separator"));
        
        System.out.println("Jackson相关JAR文件：");
        for (String path : paths) {
            if (path.toLowerCase().contains("jackson")) {
                System.out.println("   - " + path);
            }
        }
        
        System.out.println();
    }
    
    /**
     * 测试JSON序列化和反序列化
     */
    public static void testJsonOperations() {
        System.out.println("4. 测试JSON操作...");
        
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = 
                new com.fasterxml.jackson.databind.ObjectMapper();
            
            // 测试对象
            TestObject obj = new TestObject();
            obj.name = "测试";
            obj.value = 123;
            
            // 序列化
            String json = mapper.writeValueAsString(obj);
            System.out.println("   ✓ 序列化成功: " + json);
            
            // 反序列化
            TestObject result = mapper.readValue(json, TestObject.class);
            System.out.println("   ✓ 反序列化成功: " + result.name + ", " + result.value);
            
        } catch (Exception e) {
            System.out.println("   ✗ JSON操作失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试用的简单对象
     */
    public static class TestObject {
        public String name;
        public int value;
    }
}
