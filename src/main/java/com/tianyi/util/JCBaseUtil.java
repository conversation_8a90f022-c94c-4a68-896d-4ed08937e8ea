package com.tianyi.util;

import com.tianyi.model.JCBuyResultModel;
import com.tianyi.model.MatchData;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class JCBaseUtil {
    // 获取指定年月的天数
    public static int getDaysInMonth(int year, int month) {
        switch (month) {
            case 1: case 3: case 5: case 7: case 8: case 10: case 12:
                return 31;
            case 4: case 6: case 9: case 11:
                return 30;
            case 2:
                // 判断是否为闰年
                if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 0;
        }
    }

    public static String getResultMapString(Map<String, Object> map, boolean hasType) {
        String[] result = new String[1];
        result[0] = "天数:" + map.get("days");
        int day =  (Integer)map.get("days");
        map.keySet().forEach(key -> {
            if (!key.equals("days") && !key.contains("C") && !key.contains("T")) {
                int cash =  (Integer)map.get(key);
                int count =  (Integer)map.get(key+"_C");
                if (hasType) {
                    String type = (String)map.get(key+"_T");
                    if (cash != day * -100) {
                        result[0] += ("\n"+key+":"+cash+"("+count+")"+"("+type.trim()+")");
//                        result[0] += ("\n"+key+":"+cash+"("+type.trim()+")");
                    }
                } else {
                    if (cash != day * -100) {
                        result[0] += ("\n"+key+":"+cash+"("+count+")");
//                        result[0] += ("\n"+key+":"+cash);
                    }
                }
            }
        });
        return result[0];
    }

    public static void setResultMap(Map<String, Object> map, JCBuyResultModel resultModel, boolean hasType) {
        if (resultModel != null) {
            map.put("days",  (Integer)map.get("days") + 1);
            map.keySet().forEach(key -> {
                if (!key.equals("days") && !key.contains("C") && !key.contains("T")) {
                    map.put(key,  (Integer)map.get(key) - 100);
                }
            });
            String targetKey = resultModel.getResult();
            targetKey = targetKey.replace("7+", "7");
            if (map.containsKey(targetKey)) {
                String keyC = targetKey+"_C";
                int count = (Integer)map.get(keyC);
                int cashOld =  (Integer)map.get(targetKey);
                int cash = (int)calculateCash(resultModel.getOdds());
                map.put(targetKey, cashOld + cash);
                map.put(keyC, count + 1);
                if (hasType) {
                    String keyT = resultModel.getResult()+"_T";
                    map.put(keyT, map.get(keyT) + resultModel.getType() + " ");
                }
            }
        }
    }

    public static double calculateCash(String odds) {
        try {
            return 100 * Double.parseDouble(odds);
        } catch (NumberFormatException e) {
            return -100;
        }
    }

    /**
     * 打印包含格式化时间的比赛摘要
     */
    public static void printMatchSummaryWithFormattedTime(String timeStr, List<MatchData> matchList) {
        System.out.println("=== 比赛摘要（含格式化时间） ===");
        System.out.println("总共 " + matchList.size() + " 场比赛");
        System.out.println();

        System.out.printf("%-10s %-20s %-10s %-15s %-15s %-10s %-10s%n",
                "场次", "格式化时间", "联赛", "主队", "客队", "半场", "全场");
        System.out.println("=".repeat(110));

        for (MatchData match : matchList) {
            MatchData.MatchResult result = match.getResult();
            String formattedTime = TimeUtil.combineSimpleDateTime(timeStr, match.getMatchTime());

            System.out.printf("%-10s %-20s %-10s %-15s %-15s %-10s %-10s%n",
                    match.getNum(),
                    formattedTime,
                    match.getLeague(),
                    match.getHome(),
                    match.getAway(),
                    result != null ? result.getHalf() : "N/A",
                    result != null ? result.getFull() : "N/A"
            );
        }
        System.out.println();
    }

    public static void printResultData(JCBuyResultModel matchModel, String msg, boolean hasType) {
        if (matchModel != null) {
            String result = msg + "===>日期:"+ matchModel.getJcDate();
            result += (" 场次:" + matchModel.getNum());
            result += (" 信息:" + matchModel.getTeamAndOdds());
            if (hasType) {
                result += (" 强弱:" + matchModel.getType());
            }
            result += (" 结果:" + matchModel.getResult());
            result += (" 赔率:" + matchModel.getOdds());
            System.out.println(result);
        }
    }

    public static void printGoalResultData(JCBuyResultModel matchModel, String msg) {
        if (matchModel != null) {
            String result = msg + "===>日期:"+ matchModel.getJcDate();
            result += (" 结果:" + matchModel.getResult());
            result += (" 赔率:" + matchModel.getOdds());
            System.out.println(result);
        }
    }

    public static void printHalfFullCashData(Map<String, Integer> halfFullCashMap, Map<String, String> halfFullCashMapDate) {
        final String[] result = {""};
        halfFullCashMap.keySet().forEach(key -> {
            int cash = halfFullCashMap.get(key);
            String dates = halfFullCashMapDate.get(key);
            if (!"".equals(dates) && cash < 0) {
                // 移除末尾的逗号
                if (dates != null && dates.endsWith(",")) {
                    dates = dates.substring(0, dates.length() - 1);
                }
                result[0] += (key + ":" + cash + "(" + dates + ")\n");
            }
        });
        System.out.print(result[0]);
    }

    static void printBuyResult(Map<String, Object> fsMap, Map<String, Object> feMap, Map<String, Object> endMap, Map<String, Object> tmap, Map<String, Object> fMap) {
        Map<String, Object> sortFsMap = new TreeMap<>(String::compareTo);
        sortFsMap.putAll(fsMap);
        Map<String, Object> sortFeMap = new TreeMap<>(String::compareTo);
        sortFeMap.putAll(feMap);
        Map<String, Object> sortEndMap = new TreeMap<>(String::compareTo);
        sortEndMap.putAll(endMap);
        Map<String, Object> sort3Map = new TreeMap<>(String::compareTo);
        sort3Map.putAll(tmap);
        Map<String, Object> sort4Map = new TreeMap<>(String::compareTo);
        sort4Map.putAll(fMap);

        System.out.println("FS ==> ");
        System.out.println(JCBaseUtil.getResultMapString(sortFsMap, false));
        System.out.println("FE ==> ");
        System.out.println(JCBaseUtil.getResultMapString(sortFeMap, false));
        System.out.println("END ==> ");
        System.out.println(JCBaseUtil.getResultMapString(sortEndMap, false));
        System.out.println("3 ==> ");
        System.out.println(JCBaseUtil.getResultMapString(sort3Map, false));
        System.out.println("4 ==> ");
        System.out.println(JCBaseUtil.getResultMapString(sort4Map, false));
    }

    static void printBaseHandicapBuyResult(Map<String, Object> map) {
        Map<String, Object> sortMap = new TreeMap<>(String::compareTo);
        sortMap.putAll(map);
        sortMap.forEach((key, value) -> {
            System.out.println(key + "(" + value + ")");
        });
    }

    public static String getTeamString(MatchData matchData1, MatchData matchData2, MatchData matchData3, MatchData matchData4) {
        String team1 = "(" + matchData1.getHome() + "_" + matchData1.getAway() + "_" + matchData1.getResult().getWinDrawLoseOdds() + "_" + matchData1.getResult().getWinDrawLoseResult() + "_" + matchData1.getResult().getScoreResult() + ")";
        String team2 = "(" + matchData2.getHome() + "_" + matchData2.getAway() + "_" + matchData2.getResult().getWinDrawLoseOdds() + "_" + matchData2.getResult().getWinDrawLoseResult() + "_" + matchData2.getResult().getScoreResult() + ")";
        String team3 = null;  String team4 = null;
        if (matchData3 != null) {
            team3 = "(" + matchData3.getHome() + "_" + matchData3.getAway() + "_" + matchData3.getResult().getWinDrawLoseOdds() + "_" + matchData3.getResult().getWinDrawLoseResult() + "_" + matchData3.getResult().getScoreResult() + ")";
        }
        if (matchData4 != null) {
            team4 = "(" + matchData4.getHome() + "_" + matchData4.getAway() + "_" + matchData4.getResult().getWinDrawLoseOdds() + "_" + matchData4.getResult().getWinDrawLoseResult() + "_" + matchData4.getResult().getScoreResult() + ")";
        }
        String team = team1 + "-" + team2;
        if (team3 != null) {
            team += ("-" + team3);
        }
        if (team4 != null) {
            team += ("-" + team4);
        }
        return team;
    }

    public static String getTotalScore(String full) {
        String[] parts = full.split("-");
        int num1 = Integer.parseInt(parts[0]);
        int num2 = Integer.parseInt(parts[1]);
        int sum = num1 + num2;
        if (sum > 6) return "7+";
        return String.valueOf(sum);
    }

    public static String getTypeString(MatchData matchData1, MatchData matchData2, MatchData matchData3, MatchData matchData4) {
        String typeString = "";
        String handicap1 = matchData1.getResult().getHandicap();
        String handicap2 = matchData2.getResult().getHandicap();
        typeString += (handicap1.contains("-") ? "1" : "0");
        typeString += (handicap2.contains("-") ? "1" : "0");
        if (matchData3 != null) {
            String handicap3 = matchData3.getResult().getHandicap();
            typeString += (handicap3.contains("-") ? "1" : "0");
        }
        if (matchData4 != null) {
            String handicap4 = matchData4.getResult().getHandicap();
            typeString += (handicap4.contains("-") ? "1" : "0");
        }
        return typeString;
    }

    public static void setScoreMap(Map<String, Integer> map, Map<String, String> cntMap, JCBuyResultModel resultModel, int month, int day) {
        if (resultModel == null) {
            return;
        }
        String key = resultModel.getResult();
        if (map.containsKey(key)) {
            map.put(key, map.get(key) + (int)calculateCash(resultModel.getOdds()));
        } else {
            map.put(key, (int)calculateCash(resultModel.getOdds()));
        }
        if (cntMap.containsKey(key)) {
            String cntStr = cntMap.get(key);
            if (cntStr.contains(month+"月")) {
                String[] cntArr = cntStr.split("_");
                String result = "";
                for (String s : cntArr) {
                    String cnt = s;
                    if (cnt.contains(month + "月")) {
                        String[] cntDayArr = cnt.split("\\(");
                        cnt = cntDayArr[0] + "(" + cntDayArr[1].substring(0, cntDayArr[1].length() - 1) + "," + day + ")";
                    }
                    if (result.isEmpty()) {
                        result = cnt;
                    } else {
                        result = result + "_" + cnt;
                    }
                }
                cntMap.put(key, result);
            } else {
                cntMap.put(key, cntStr + "_" + month + "月(" + day + ")");
            }
        } else {
            cntMap.put(key, month + "月(" + day + ")");
        }
    }

    public static void printScoreMap(Map<String, Integer> map, Map<String, String> cntMap) {
        map.forEach((key, value) -> {
            if (!key.equals("X")) {
                System.out.println(key + "  " + value + "  " + cntMap.get(key));
            }
        });
    }
}
