package com.tianyi.util;

import com.tianyi.sql.JCFSResultSQLStatements;
import com.tianyi.sql.JCFEResultSQLStatements;
import com.tianyi.sql.JCEndResultSQLStatements;
import java.sql.*;

/**
 * 竞彩购买结果数据工具类
 * 用于插入JCFSResultTable、JCFEResultTable、JCEndResultTable三个表的数据
 */
public class JCBuyResultDataUtil {
    
    /**
     * 插入JC固定场次(FS)购买结果数据
     * 
     * @param jcDate 竞彩日期
     * @param num1 第一场比赛场次编号
     * @param num2 第二场比赛场次编号
     * @param winLoseCash11 胜平负平平中奖金额
     * @param handicapCashData 让球玩法中奖金额数组[33,31,30,13,11,10,03,01,00]
     * @param halfFullCashData 半全场玩法中奖金额数组(49个元素)
     * @param scoreCashData 总进球数玩法中奖金额数组(25个元素)
     * @param handicapDrawCashData 让球+平玩法中奖金额数组(6个元素)
     * @return 是否插入成功
     */
    public static boolean insertFSBuyResultData(String jcDate, String num1, String num2,
                                               String winLoseCash11,
                                               String[] handicapCashData,
                                               String[] halfFullCashData,
                                               String[] scoreCashData,
                                               String[] handicapDrawCashData) {
        
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCFSResultSQLStatements.getInsertFSBuyResultTableSQL());
            
            int paramIndex = 1;
            
            // 基本信息
            statement.setString(paramIndex++, jcDate);
            statement.setString(paramIndex++, num1);
            statement.setString(paramIndex++, num2);
            
            // 胜平负
            statement.setString(paramIndex++, winLoseCash11);
            
            // 让球玩法 (9个参数)
            for (int i = 0; i < 9 && i < handicapCashData.length; i++) {
                statement.setString(paramIndex++, handicapCashData[i]);
            }
            
            // 半全场玩法 (49个参数)
            for (int i = 0; i < 49 && i < halfFullCashData.length; i++) {
                statement.setString(paramIndex++, halfFullCashData[i]);
            }
            
            // 总进球数玩法 (25个参数)
            for (int i = 0; i < 25 && i < scoreCashData.length; i++) {
                statement.setString(paramIndex++, scoreCashData[i]);
            }
            
            // 让球+平玩法 (6个参数)
            for (int i = 0; i < 6 && i < handicapDrawCashData.length; i++) {
                statement.setString(paramIndex++, handicapDrawCashData[i]);
            }
            
            int result = statement.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ JC固定场次(FS)购买结果数据插入成功");
                return true;
            } else {
                System.out.println("❌ JC固定场次(FS)购买结果数据插入失败");
                return false;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ 插入JC固定场次(FS)购买结果数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
    
    /**
     * 插入JC固定场次(FE)购买结果数据
     */
    public static boolean insertFEBuyResultData(String jcDate, String num1, String num2,
                                               String winLoseCash11,
                                               String[] handicapCashData,
                                               String[] halfFullCashData,
                                               String[] scoreCashData,
                                               String[] handicapDrawCashData) {
        
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCFEResultSQLStatements.getInsertFEBuyResultTableSQL());
            
            int paramIndex = 1;
            
            // 基本信息
            statement.setString(paramIndex++, jcDate);
            statement.setString(paramIndex++, num1);
            statement.setString(paramIndex++, num2);
            
            // 胜平负
            statement.setString(paramIndex++, winLoseCash11);
            
            // 让球玩法 (9个参数)
            for (int i = 0; i < 9 && i < handicapCashData.length; i++) {
                statement.setString(paramIndex++, handicapCashData[i]);
            }
            
            // 半全场玩法 (49个参数)
            for (int i = 0; i < 49 && i < halfFullCashData.length; i++) {
                statement.setString(paramIndex++, halfFullCashData[i]);
            }
            
            // 总进球数玩法 (25个参数)
            for (int i = 0; i < 25 && i < scoreCashData.length; i++) {
                statement.setString(paramIndex++, scoreCashData[i]);
            }
            
            // 让球+平玩法 (6个参数)
            for (int i = 0; i < 6 && i < handicapDrawCashData.length; i++) {
                statement.setString(paramIndex++, handicapDrawCashData[i]);
            }
            
            int result = statement.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ JC固定场次(FE)购买结果数据插入成功");
                return true;
            } else {
                System.out.println("❌ JC固定场次(FE)购买结果数据插入失败");
                return false;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ 插入JC固定场次(FE)购买结果数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
    
    /**
     * 插入JC结束场次(End)购买结果数据
     */
    public static boolean insertEndBuyResultData(String jcDate, String num1, String num2,
                                                String winLoseCash11,
                                                String[] handicapCashData,
                                                String[] halfFullCashData,
                                                String[] scoreCashData,
                                                String[] handicapDrawCashData) {
        
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.prepareStatement(JCEndResultSQLStatements.getInsertEndBuyResultTableSQL());
            
            int paramIndex = 1;
            
            // 基本信息
            statement.setString(paramIndex++, jcDate);
            statement.setString(paramIndex++, num1);
            statement.setString(paramIndex++, num2);
            
            // 胜平负
            statement.setString(paramIndex++, winLoseCash11);
            
            // 让球玩法 (9个参数)
            for (int i = 0; i < 9 && i < handicapCashData.length; i++) {
                statement.setString(paramIndex++, handicapCashData[i]);
            }
            
            // 半全场玩法 (49个参数)
            for (int i = 0; i < 49 && i < halfFullCashData.length; i++) {
                statement.setString(paramIndex++, halfFullCashData[i]);
            }
            
            // 总进球数玩法 (25个参数)
            for (int i = 0; i < 25 && i < scoreCashData.length; i++) {
                statement.setString(paramIndex++, scoreCashData[i]);
            }
            
            // 让球+平玩法 (6个参数)
            for (int i = 0; i < 6 && i < handicapDrawCashData.length; i++) {
                statement.setString(paramIndex++, handicapDrawCashData[i]);
            }
            
            int result = statement.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ JC结束场次(End)购买结果数据插入成功");
                return true;
            } else {
                System.out.println("❌ JC结束场次(End)购买结果数据插入失败");
                return false;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ 插入JC结束场次(End)购买结果数据失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }
    
    /**
     * 创建示例数据数组
     */
    public static String[] createSampleHandicapData() {
        String[] data = new String[9];
        for (int i = 0; i < 9; i++) {
            data[i] = "-100";
        }
        return data;
    }
    
    public static String[] createSampleHalfFullData() {
        String[] data = new String[49];
        for (int i = 0; i < 49; i++) {
            data[i] = (i % 5 == 0) ? "20.00" : "0.00"; // 每5个设置一个非零值
        }
        return data;
    }
    
    public static String[] createSampleScoreData() {
        String[] data = new String[25];
        for (int i = 0; i < 25; i++) {
            data[i] = "-100";
        }
        return data;
    }
    
    public static String[] createSampleHandicapDrawData() {
        String[] data = new String[6];
        for (int i = 0; i < 6; i++) {
            data[i] = "-100";
        }
        return data;
    }
}
