package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;

/**
 * 购买结果数据工具类
 */
public class JCBuyResultDataUtil {

    private static final BuyTableDAO buyTableDAO = new BuyTableDAO();

    public static boolean searchHalfFullBuyResultData(String date, String num) {
        return buyTableDAO.searchHalfFullBuyResultByDateAndNum(date, num);
    }

    public static boolean searchHalfFullBuyResultDataByFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        return buyTableDAO.searchHalfFullBuyResultByDateAndNumAndFsFeEnd(date, num, fsMatch, feMatch, endMatch);
    }

    public static boolean searchHandicapBuyResultData(String date, String num) {
        return buyTableDAO.searchHandicapBuyResultByDateAndNum(date, num);
    }

    public static boolean searchHandicapBuyResultDataByFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        return buyTableDAO.searchHandicapBuyResultByDateAndNumAndFsFeEnd(date, num, fsMatch, feMatch, endMatch);
    }

    public static boolean searchScoreBuyResultData(String date, String num) {
        return buyTableDAO.searchScoreBuyResultByDateAndNum(date, num);
    }

    public static boolean searchScoreBuyResultDataByFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        return buyTableDAO.searchScoreBuyResultByDateAndNumAndFsFeEnd(date, num, fsMatch, feMatch, endMatch);
    }

    public static boolean searchGoalBuyResultData(String date, String num) {
        return buyTableDAO.searchGoalBuyResultByDateAndNum(date, num);
    }

    public static boolean searchGoalBuyResultDataByFsFeEnd(String date, String num, int fsMatch, int feMatch, int endMatch) {
        return buyTableDAO.searchGoalBuyResultByDateAndNumAndFsFeEnd(date, num, fsMatch, feMatch, endMatch);
    }

    public static void insertHalfFullBuyResultData(String date, String num, String result, String odds, String teamAndOdds, String type, int lastMatch, int feMatch, int fsMatch, int matchNum) {
        buyTableDAO.insertHalfFullBuyResultData(date, num, result, odds, teamAndOdds, type, lastMatch, feMatch, fsMatch, matchNum);
    }

    public static void insertHandicapBuyResultData(String date, String num, String result, String odds, String teamAndOdds, String type, int lastMatch, int feMatch, int fsMatch, int matchNum) {
        buyTableDAO.insertHandicapBuyResultData(date, num, result, odds, teamAndOdds, type, lastMatch, feMatch, fsMatch, matchNum);
    }

    public static void insertGoalBuyResultData(String date, String num, String result, String odds, String teamAndOdds, int lastMatch, int feMatch, int fsMatch, int matchNum) {
        buyTableDAO.insertGoalBuyResultData(date, num, result, odds, teamAndOdds, lastMatch, feMatch, fsMatch, matchNum);
    }

    public static void insertScoreBuyResultData(String date, String num, String result, String odds, String teamAndOdds, int lastMatch, int feMatch, int fsMatch, int matchNum) {
        buyTableDAO.insertScoreBuyResultData(date, num, result, odds, teamAndOdds, lastMatch, feMatch, fsMatch, matchNum);
    }
}
