package com.tianyi.util;

import com.tianyi.dao.JCTableDAO;
import com.tianyi.model.MatchData;

import java.sql.ResultSet;
import java.util.ArrayList;

/**
 * 竞彩数据工具类
 * 提供便捷的数据插入方法
 */
public class JCDataUtil {
    
    private static final JCTableDAO dao = new JCTableDAO();
    
    /**
     * 根据您提供的JSON数据格式插入比赛数据（包含竞彩时间）
     *
     * JSON格式示例：
     * {
     *   "num": "周六018",
     *   "match_time": "06-01 00:45",
     *   "matchId": "1282037",
     *   "league": "挪超",
     *   "home": "特罗姆瑟",
     *   "away": "瓦勒伦加",
     *   "result": {
     *     "half": "0-0",
     *     "full": "2-1",
     *     "win_draw_lose_result": "3",
     *     "win_draw_lose_odds": "1.700",
     *     "handicap": "-1",
     *     "handicap_result": "1",
     *     "handicap_odds": "3.300",
     *     "score_result": "2:1",
     *     "score_odds": "7.000",
     *     "goal_result": "3",
     *     "goal_odds": "3.500",
     *     "half_full_result": "1-3",
     *     "half_full_odds": "4.000"
     *   }
     * }
     */
    public static boolean insertMatchData(String num, String jcTime, String matchTime,
                                         String league, String home, String away,
                                         String half, String full,
                                         String winDrawLoseResult, String winDrawLoseOdds,
                                         String handicap, String handicapResult, String handicapOdds,
                                         String scoreResult, String scoreOdds,
                                         String goalResult, String goalOdds,
                                         String halfFullResult, String halfFullOdds) {

        return dao.insertMatch(num, jcTime, matchTime, league, home, away,
                              half, full, winDrawLoseResult, winDrawLoseOdds,
                              handicap, handicapResult, handicapOdds,
                              scoreResult, scoreOdds, goalResult, goalOdds,
                              halfFullResult, halfFullOdds);
    }

    
    /**
     * 查询时间范围内的比赛数据
     */
    public static void showMatchesByDateRange(String startDate, String endDate) {
        dao.getMatchesByDateRange(startDate, endDate);
    }
    
    /**
     * 查询所有比赛数据
     */
    public static void showAllMatches() {
        dao.getAllMatches();
    }
    
    /**
     * 根据联赛查询比赛数据
     */
    public static void showMatchesByLeague(String league) {
        dao.getMatchesByLeague(league);
    }
    
    /**
     * 获取比赛总数
     */
    public static int getMatchCount() {
        return dao.getMatchCount();
    }

    /**
     * 查询某天的比赛数据
     */
    public static ArrayList<MatchData> getMatchesByDate(String date) {
        return dao.getMatchesByDate(date);
    }
}
