package com.tianyi.util;

import java.io.IOException;
import java.nio.file.*;
import java.util.List;
import java.util.ArrayList;

public class JCFileUtil {

    /**
     * 获取指定路径下的所有子文件（递归查找）
     *
     * @param rootPath 根目录路径
     * @return 所有子文件的路径列表
     */
    public static List<String> getAllSubFiles(String rootPath) {
        List<String> filePaths = new ArrayList<>();

        Path root = Paths.get(rootPath);

        try {
            // 使用 Files.walk 遍历所有子文件
            Files.walk(root)
                    .filter(path -> !Files.isDirectory(path)) // 过滤掉目录
                    .forEach(path -> filePaths.add(path.toString()));
        } catch (IOException e) {
            System.err.println("读取文件失败: " + e.getMessage());
        }

        return filePaths;
    }
}
