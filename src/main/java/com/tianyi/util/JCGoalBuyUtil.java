package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.model.JCBuyResultModel;
import com.tianyi.model.MatchData;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

public class JCGoalBuyUtil {

    public static void queryBuyResultByMonthData(int year, int month) {
        final BuyTableDAO buyTableDAO = new BuyTableDAO();

        // 获取该月的天数
        int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

        Map<String, Object> fsGoalMap = getGoal2ResultMap();
        Map<String, Object> feGoalMap = getGoal2ResultMap();
        Map<String, Object> endGoalMap = getGoal2ResultMap();
        Map<String, Object> goal3Map = getGoal3ResultMap();
        Map<String, Object> goal4Map = getGoal4ResultMap();

        for (int day = 1; day <= daysInMonth; day++) {
            String dateStr = String.format("%d-%02d-%02d", year, month, day);
            boolean isExist = buyTableDAO.searchHalfFullBuyResultByDate(dateStr);
            if (!isExist) {
                continue;
            }

            JCBuyResultModel goalResultModel_FS = buyTableDAO.searchGoalBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 1, 0, 0);
            JCBuyResultModel goalResultModel_FE = buyTableDAO.searchGoalBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 1, 0);
            JCBuyResultModel goalResultModel_END = buyTableDAO.searchGoalBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 0, 1);
            JCBuyResultModel goalResultModel4 = buyTableDAO.searchGoalBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 3, 0, 0, 0);
            JCBuyResultModel goalResultModel5 = buyTableDAO.searchGoalBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 4, 0, 0, 0);
            JCBaseUtil.setResultMap(fsGoalMap, goalResultModel_FS, false);
            JCBaseUtil.setResultMap(feGoalMap, goalResultModel_FE, false);
            JCBaseUtil.setResultMap(endGoalMap, goalResultModel_END, false);
            JCBaseUtil.setResultMap(goal3Map, goalResultModel4, false);
            JCBaseUtil.setResultMap(goal4Map, goalResultModel5, false);

//            JCBaseUtil.printGoalResultData(goalResultModel_FS, "FS");
//            JCBaseUtil.printGoalResultData(goalResultModel_FE, "FE");
//            JCBaseUtil.printGoalResultData(goalResultModel_END, "END");
//            JCBaseUtil.printGoalResultData(goalResultModel4, "3");
//            JCBaseUtil.printGoalResultData(goalResultModel5, "4");
        }

        JCBaseUtil.printBuyResult(fsGoalMap, feGoalMap, endGoalMap, goal3Map, goal4Map);
    }

    private static Map<String, Object> getGoal2ResultMap() {
        int max = 7;
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        for (int i = 0; i <= max; i++) {
            for (int j = 0; j <= max; j++) {
                String key = i + "_" + j;
                map.put(key, 0);
                map.put(key+"_C", 0);
            }
        }
        return  map;
    }

    private static Map<String, Object> getGoal3ResultMap() {
        int max = 7;
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数

        for (int i = 0; i <= max; i++) {
            for (int j = 0; j <= max; j++) {
                for (int k = 0; k <= max; k++) {
                    String key = i + "_" + j + "_" + k;
                    map.put(key, 0);
                    map.put(key+"_C", 0);
                }
            }
        }
        return  map;
    }

    private static Map<String, Object> getGoal4ResultMap() {
        int max = 7;
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数

        for (int i = 0; i <= max; i++) {
            for (int j = 0; j <= max; j++) {
                for (int k = 0; k <= max; k++) {
                    for (int l = 0; l <= max; l++) {
                        String key = i + "_" + j + "_" + k + "_" + l;
                        map.put(key, 0);
                        map.put(key+"_C", 0);
                    }
                }
            }
        }
        return  map;
    }

    public static void setGoalResultData2(String dateStr, MatchData matchData1, MatchData matchData2, int lastMatch, int feMatch, int fsMatch) {
        String num = matchData1.getNum() + "_" + matchData2.getNum();
        boolean isExist = JCBuyResultDataUtil.searchGoalBuyResultDataByFsFeEnd(dateStr, num, fsMatch, feMatch, lastMatch);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, "X", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        if ("".equals(matchData1.getResult().getGoalResult()) || "".equals(matchData2.getResult().getGoalResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, "X", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        String result_full1 = matchData1.getResult().getFull();
        String result_full2 = matchData2.getResult().getFull();

        String team = JCBaseUtil.getTeamString(matchData1, matchData2, null, null);
        String result_full = JCBaseUtil.getTotalScore(result_full1) + "_" + JCBaseUtil.getTotalScore(result_full2);
        double odds_full = Double.parseDouble(matchData1.getResult().getGoalOdds()) * Double.parseDouble(matchData2.getResult().getGoalOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, result_full, bd.toString(), team, lastMatch, feMatch, fsMatch, 2);
    }

    public static void setGoalResultData3(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum();
        boolean isExist = JCBuyResultDataUtil.searchGoalBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull()) || "-".equals(matchData3.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 3);
            return;
        }
        if ("".equals(matchData1.getResult().getGoalResult()) || "".equals(matchData2.getResult().getGoalResult()) || "".equals(matchData3.getResult().getGoalResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 3);
            return;
        }
        String result_full1 = matchData1.getResult().getFull();
        String result_full2 = matchData2.getResult().getFull();
        String result_full3 = matchData3.getResult().getFull();
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, null);
        String result_full = JCBaseUtil.getTotalScore(result_full1) + "_" + JCBaseUtil.getTotalScore(result_full2) + "_" + JCBaseUtil.getTotalScore(result_full3);

        double odds_full = Double.parseDouble(matchData1.getResult().getGoalOdds()) * Double.parseDouble(matchData2.getResult().getGoalOdds()) * Double.parseDouble(matchData3.getResult().getGoalOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, result_full, bd.toString(), team, 0, 0, 0, 3);
    }

    public static void setGoalResultData4(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3, MatchData matchData4) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum() + "_" + matchData4.getNum();
        boolean isExist = JCBuyResultDataUtil.searchGoalBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull()) || "-".equals(matchData3.getResult().getFull()) || "-".equals(matchData4.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 4);
            return;
        }
        if ("".equals(matchData1.getResult().getGoalResult()) || "".equals(matchData2.getResult().getGoalResult()) || "".equals(matchData3.getResult().getGoalResult()) || "".equals(matchData4.getResult().getGoalResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 4);
            return;
        }
        String result_full1 = matchData1.getResult().getFull();
        String result_full2 = matchData2.getResult().getFull();
        String result_full3 = matchData3.getResult().getFull();
        String result_full4 = matchData4.getResult().getFull();
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, matchData4);
        String result_full = JCBaseUtil.getTotalScore(result_full1) + "_" + JCBaseUtil.getTotalScore(result_full2) + "_" + JCBaseUtil.getTotalScore(result_full3) + "_" + JCBaseUtil.getTotalScore(result_full4);

        double odds_full = Double.parseDouble(matchData1.getResult().getGoalOdds()) * Double.parseDouble(matchData2.getResult().getGoalOdds()) * Double.parseDouble(matchData3.getResult().getGoalOdds()) * Double.parseDouble(matchData4.getResult().getGoalOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertGoalBuyResultData(dateStr, num, result_full, bd.toString(), team, 0, 0, 0, 4);
    }
}
