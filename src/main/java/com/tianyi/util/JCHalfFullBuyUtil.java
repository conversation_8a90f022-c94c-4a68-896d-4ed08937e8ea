package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.model.JCBuyResultModel;
import com.tianyi.model.MatchData;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.tianyi.util.JCBaseUtil.calculateCash;

public class JCHalfFullBuyUtil {
    public static void queryBuyResultByMonthData(int year) {
        final BuyTableDAO buyTableDAO = new BuyTableDAO();

        Map<String, Integer> fsMap = new HashMap<>();
        Map<String, Integer> feMap = new HashMap<>();
        Map<String, Integer> endMap = new HashMap<>();
        Map<String, Integer> match3Map = new HashMap<>();
        Map<String, Integer> match4Map = new HashMap<>();

        Map<String, String> fsCntMap = new HashMap<>();
        Map<String, String> feCntMap = new HashMap<>();
        Map<String, String> endCntMap = new HashMap<>();
        Map<String, String> match3CntMap = new HashMap<>();
        Map<String, String> match4CntMap = new HashMap<>();

        for (int month = 1; month <= 12; month++) {
            // 获取该月的天数
            int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++) {
                String dateStr = String.format("%d-%02d-%02d", year, month, day);
                boolean isExist = buyTableDAO.searchHalfFullBuyResultByDate(dateStr);
                if (!isExist) {
                    continue;
                }
                JCBuyResultModel halfFullResultModel_FS = buyTableDAO.searchHalfFullBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 1, 0, 0);
                JCBuyResultModel halfFullResultModel_FE = buyTableDAO.searchHalfFullBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 1, 0);
                JCBuyResultModel halfFullResultModel_END = buyTableDAO.searchHalfFullBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 0, 1);
                JCBuyResultModel halfFullResultModel4 = buyTableDAO.searchHalfFullBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 3, 0, 0, 0);
                JCBuyResultModel halfFullResultModel5 = buyTableDAO.searchHalfFullBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 4, 0, 0, 0);

                JCBaseUtil.setScoreMap(fsMap, fsCntMap, halfFullResultModel_FS, month, day);
                JCBaseUtil.setScoreMap(feMap, feCntMap, halfFullResultModel_FE, month, day);
                JCBaseUtil.setScoreMap(endMap, endCntMap, halfFullResultModel_END, month, day);
                JCBaseUtil.setScoreMap(match3Map, match3CntMap, halfFullResultModel4, month, day);
                JCBaseUtil.setScoreMap(match4Map, match4CntMap, halfFullResultModel5, month, day);
            }
        }

        Map<String, Integer> sortFsMap = new TreeMap<>(String::compareTo);
        sortFsMap.putAll(fsMap);
        Map<String, Integer> sortFeMap = new TreeMap<>(String::compareTo);
        sortFeMap.putAll(feMap);
        Map<String, Integer> sortEndMap = new TreeMap<>(String::compareTo);
        sortEndMap.putAll(endMap);
        Map<String, Integer> sort3Map = new TreeMap<>(String::compareTo);
        sort3Map.putAll(match3Map);
        Map<String, Integer> sort4Map = new TreeMap<>(String::compareTo);
        sort4Map.putAll(match4Map);

        System.out.println("------ FS -------");
        JCBaseUtil.printScoreMap(sortFsMap, fsCntMap);
        System.out.println("------ FE -------");
        JCBaseUtil.printScoreMap(sortFeMap, feCntMap);
        System.out.println("------ END -------");
        JCBaseUtil.printScoreMap(sortEndMap, endCntMap);
        System.out.println("------ M3 -------");
        JCBaseUtil.printScoreMap(sort3Map, match3CntMap);
        System.out.println("------ M4 -------");
        JCBaseUtil.printScoreMap(sort4Map, match4CntMap);
    }

    public static void queryBuyResultByMonthDetailData(int year) {

        for (int month = 1; month <= 12; month++) {
            // 获取该月的天数
            int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++) {
                String dateStr = String.format("%d-%02d-%02d", year, month, day);
                // 获取指定日期的所有比赛
                ArrayList<MatchData> list = JCDataUtil.getMatchesByDate(dateStr);
                // 检查是否有足够的比赛数据
                if (list.size() < 2) {
                    continue;
                }

                MatchData firstMatch = list.get(0);
                MatchData secondMatch = list.get(1);
                MatchData end1Match = null;
                MatchData end2Match = null;
                if (list.size() > 3) {
                    end1Match = list.get(list.size() - 1);
                    end2Match = list.get(list.size() - 2);
                } else if (list.size() > 2) {
                    end1Match = list.get(list.size() - 1);
                }

                String result_string = getTeamNameString(firstMatch) + " " + getTeamNameString(secondMatch);
                if (end2Match != null) {
                    result_string = result_string + " " + getTeamNameString(end2Match);
                }
                if (end1Match != null) {
                    result_string = result_string + " " + getTeamNameString(end1Match);
                }
                System.out.println(dateStr + " " + JCBaseUtil.getTypeString(firstMatch, secondMatch, end2Match, end1Match) + " " + result_string);
            }
            System.out.println();
        }
    }

    private static String getTeamNameString(MatchData matchData) {
        return matchData.getHome() + "_" + matchData.getAway() + "_" + matchData.getResult().getScoreResult() + "_" + matchData.getResult().getHandicapResult();
    }

    private static Map<String, Object> getHalfFull2ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        int[] values = {0, 1, 3};

        for (int first : values) {
            for (int second : values) {
                for (int third : values) {
                    for (int fourth : values) {
                        String key = first + "" + second + "_" + third + "" + fourth;
                        map.put(key, 0);
                        map.put(key+"_C", 0);
                        map.put(key+"_T", "");
                    }
                }
            }
        }
        return  map;
    }

    private static Map<String, Object> getHalfFull3ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        int[] values = {0, 1, 3};

        for (int first : values) {
            for (int second : values) {
                for (int third : values) {
                    for (int fourth : values) {
                        for (int fifth : values) {
                            for (int sixth : values) {
                                String key = first + "" + second + "_" + third + "" + fourth + "_" + fifth + "" + sixth;
                                map.put(key, 0);
                                map.put(key + "_C", 0);
                                map.put(key+"_T", "");
                            }
                        }
                    }
                }
            }
        }
        return  map;
    }

    private static Map<String, Object> getHalfFull4ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        int[] values = {0, 1, 3};

        for (int first : values) {
            for (int second : values) {
                for (int third : values) {
                    for (int fourth : values) {
                        for (int fifth : values) {
                            for (int sixth : values) {
                                for (int seventh : values) {
                                    for (int eighth : values) {
                                        String key = first + "" + second + "_" + third + "" + fourth + "_" +
                                                fifth + "" + sixth + "_" + seventh + "" + eighth;
                                        map.put(key, 0);
                                        map.put(key + "_C", 0);
                                        map.put(key+"_T", "");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return  map;
    }

    public static void setHalfFullMatchResultData2(String dateStr, MatchData matchData1, MatchData matchData2, int lastMatch, int feMatch, int fsMatch) {
        String num = matchData1.getNum() + "_" + matchData2.getNum();
        boolean isExist = JCBuyResultDataUtil.searchHalfFullBuyResultDataByFsFeEnd(dateStr, num, fsMatch, feMatch, lastMatch);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, "X", "", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        if ("".equals(matchData1.getResult().getHalfFullResult()) || "".equals(matchData2.getResult().getHalfFullResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, "X", "", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        String result_half_full1 = matchData1.getResult().getHalfFullResult().replace("-", "");
        String result_half_full2 = matchData2.getResult().getHalfFullResult().replace("-", "");
        String typeString = JCBaseUtil.getTypeString(matchData1, matchData2, null, null);
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, null, null);
        String result_half_full = result_half_full1+ "_" + result_half_full2;
        double odds_half_full = Double.parseDouble(matchData1.getResult().getHalfFullOdds()) * Double.parseDouble(matchData2.getResult().getHalfFullOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_half_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, result_half_full, bd.toString(), team, typeString, lastMatch, feMatch, fsMatch, 2);
    }

    public static void setHalfFullMatchResultData3(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum();
        boolean isExist = JCBuyResultDataUtil.searchHalfFullBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull()) || "-".equals(matchData3.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, "X", "", "", "", 0, 0, 0, 3);
            return;
        }
        if ("".equals(matchData1.getResult().getHalfFullResult()) || "".equals(matchData2.getResult().getHalfFullResult()) || "".equals(matchData3.getResult().getHalfFullResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, "X", "", "", "", 0, 0, 0, 3);
            return;
        }
        String result_half_full1 = matchData1.getResult().getHalfFullResult().replace("-", "");
        String result_half_full2 = matchData2.getResult().getHalfFullResult().replace("-", "");
        String result_half_full3 = matchData3.getResult().getHalfFullResult().replace("-", "");
        String typeString = JCBaseUtil.getTypeString(matchData1, matchData2, matchData3, null);
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, null);
        String result_half_full = result_half_full1 + "_" + result_half_full2 + "_" + result_half_full3;
        double odds_half_full = Double.parseDouble(matchData1.getResult().getHalfFullOdds()) * Double.parseDouble(matchData2.getResult().getHalfFullOdds()) * Double.parseDouble(matchData3.getResult().getHalfFullOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_half_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, result_half_full, bd.toString(), team, typeString, 0, 0, 0, 3);
    }

    public static void setHalfFullMatchResultData4(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3, MatchData matchData4) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum() + "_" + matchData4.getNum();
        boolean isExist = JCBuyResultDataUtil.searchHalfFullBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull()) || "-".equals(matchData3.getResult().getFull()) || "-".equals(matchData4.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, "X", "", "", "", 0, 0, 0, 4);
            return;
        }
        if ("".equals(matchData1.getResult().getHalfFullResult()) || "".equals(matchData2.getResult().getHalfFullResult()) || "".equals(matchData3.getResult().getHalfFullResult()) || "".equals(matchData4.getResult().getHalfFullResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, "X", "", "", "", 0, 0, 0, 4);
            return;
        }
        String result_half_full1 = matchData1.getResult().getHalfFullResult().replace("-", "");
        String result_half_full2 = matchData2.getResult().getHalfFullResult().replace("-", "");
        String result_half_full3 = matchData3.getResult().getHalfFullResult().replace("-", "");
        String result_half_full4 = matchData4.getResult().getHalfFullResult().replace("-", "");
        String typeString = JCBaseUtil.getTypeString(matchData1, matchData2, matchData3, matchData4);
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, matchData4);
        String result_half_full = result_half_full1 + "_" + result_half_full2 + "_" + result_half_full3 + "_" + result_half_full4;
        double odds_half_full = Double.parseDouble(matchData1.getResult().getHalfFullOdds()) * Double.parseDouble(matchData2.getResult().getHalfFullOdds()) * Double.parseDouble(matchData3.getResult().getHalfFullOdds()) * Double.parseDouble(matchData4.getResult().getHalfFullOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_half_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertHalfFullBuyResultData(dateStr, num, result_half_full, bd.toString(), team, typeString, 0, 0, 0, 4);
    }
}
