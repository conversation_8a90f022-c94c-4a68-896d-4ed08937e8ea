package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.model.JCBuyResultModel;
import com.tianyi.model.MatchData;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class JCHandicapBuyUtil {

    public static void queryBuyResultByMonthData(int year, int month) {
        final BuyTableDAO buyTableDAO = new BuyTableDAO();

        // 获取该月的天数
        int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

        Map<String, Object> fsHandicapFullMap = getHandicapResultMap();
        Map<String, Object> feHandicapFullMap = getHandicapResultMap();
        Map<String, Object> endHandicapFullMap = getHandicapResultMap();
        Map<String, Object> handicap3Map = getHandicap3ResultMap();
        Map<String, Object> handicap4Map = getHandicap4ResultMap();

        for (int day = 1; day <= daysInMonth; day++) {
            String dateStr = String.format("%d-%02d-%02d", year, month, day);
            boolean isExist = buyTableDAO.searchHalfFullBuyResultByDate(dateStr);
            if (!isExist) {
                continue;
            }
            JCBuyResultModel handicapResultModel_FS = buyTableDAO.searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 1, 0, 0);
            JCBuyResultModel handicapResultModel_FE = buyTableDAO.searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 1, 0);
            JCBuyResultModel handicapResultModel_END = buyTableDAO.searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 0, 1);
            JCBuyResultModel handicapResultModel4 = buyTableDAO.searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 3, 0, 0, 0);
            JCBuyResultModel handicapResultModel5 = buyTableDAO.searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 4, 0, 0, 0);

            JCBaseUtil.setResultMap(fsHandicapFullMap, handicapResultModel_FS, true);
            JCBaseUtil.setResultMap(feHandicapFullMap, handicapResultModel_FE, true);
            JCBaseUtil.setResultMap(endHandicapFullMap, handicapResultModel_END, true);
            JCBaseUtil.setResultMap(handicap3Map, handicapResultModel4, true);
            JCBaseUtil.setResultMap(handicap4Map, handicapResultModel5, true);
        }
        JCBaseUtil.printBuyResult(fsHandicapFullMap, feHandicapFullMap, endHandicapFullMap, handicap3Map, handicap4Map);
    }

    public static void querySpecialBuyResultByMonthData(int year, int month) {
        final BuyTableDAO buyTableDAO = new BuyTableDAO();

        // 获取该月的天数
        int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

        double[] cash_fs = {0,0,0,0,0,0,0,0,0};
        double[] cash_fe = {0,0,0,0,0,0,0,0,0};
        double[] cash_end = {0,0,0,0,0,0,0,0,0};

        Map<String, Object> map2_fs = new HashMap<>();
        Map<String, Object> map2_fe = new HashMap<>();
        Map<String, Object> map2_end = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        Map<String, Object> map4 = new HashMap<>();

        for (int day = 1; day <= daysInMonth; day++) {
            String dateStr = String.format("%d-%02d-%02d", year, month, day);
            boolean isExist = buyTableDAO.searchHalfFullBuyResultByDate(dateStr);
            if (!isExist) {
                continue;
            }

            queryStrongHandicapResultByDate(dateStr, buyTableDAO, cash_fs, cash_fe, cash_end, 0);
            queryStrongHandicapResultByDate(dateStr, buyTableDAO, cash_fs, cash_fe, cash_end, 1);
            queryStrongHandicapResultByDate(dateStr, buyTableDAO, cash_fs, cash_fe, cash_end, 2);
            queryBaseHandicapResultByDate(dateStr, buyTableDAO, 2 ,map2_fs, 1, 0, 0);
            queryBaseHandicapResultByDate(dateStr, buyTableDAO, 2 ,map2_fe, 0, 1, 0);
            queryBaseHandicapResultByDate(dateStr, buyTableDAO, 2 ,map2_end, 0, 0, 1);
            queryBaseHandicapResultByDate(dateStr, buyTableDAO, 3, map3, 0, 0, 0);
            queryBaseHandicapResultByDate(dateStr, buyTableDAO, 4 ,map4, 0, 0, 0);
        }
        System.out.println("==== 2FS ====");
        JCBaseUtil.printBaseHandicapBuyResult(map2_fs);
        System.out.println("==== 2FS-S ====");
        System.out.println("正正 => "+ cash_fs[0] + "\n负负 => "+ cash_fs[1] + "\n负正 => "+ cash_fs[2] + "\n正负 => "+ cash_fs[3] + "\n正平 => "+ cash_fs[4] + "\n平平 => "+ cash_fs[5] + "\n负平 => "+ cash_fs[6] + "\n平正 => "+ cash_fs[7] + "\n平负 => "+ cash_fs[8]);
        System.out.println("==== 2FE ====");
        JCBaseUtil.printBaseHandicapBuyResult(map2_fe);
        System.out.println("==== 2FE-S ====");
        System.out.println("正正 => "+ cash_fe[0] + "\n负负 => "+ cash_fe[1] + "\n负正 => "+ cash_fe[2] + "\n正负 => "+ cash_fe[3] + "\n正平 => "+ cash_fe[4] + "\n平平 => "+ cash_fe[5] + "\n负平 => "+ cash_fe[6] + "\n平正 => "+ cash_fe[7] + "\n平负 => "+ cash_fe[8]);
        System.out.println("==== 2END ====");
        JCBaseUtil.printBaseHandicapBuyResult(map2_end);
        System.out.println("==== 2END-S ====");
        System.out.println("正正 => "+ cash_end[0] + "\n负负 => "+ cash_end[1] + "\n负正 => "+ cash_end[2] + "\n正负 => "+ cash_end[3] + "\n正平 => "+ cash_end[4] + "\n平平 => "+ cash_end[5] + "\n负平 => "+ cash_end[6] + "\n平正 => "+ cash_end[7] + "\n平负 => "+ cash_end[8]);
        System.out.println("==== 3 ====");
        JCBaseUtil.printBaseHandicapBuyResult(map3);
        System.out.println("==== 4 ====");
        JCBaseUtil.printBaseHandicapBuyResult(map4);
    }

    public static void setHandicapResultData2(String dateStr, MatchData matchData1, MatchData matchData2, int lastMatch, int feMatch, int fsMatch) {
        String num = matchData1.getNum() + "_" + matchData2.getNum();
        boolean isExist = JCBuyResultDataUtil.searchHandicapBuyResultDataByFsFeEnd(dateStr, num, fsMatch, feMatch, lastMatch);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("延期".equals(matchData1.getResult().getHandicapResult()) || "延期".equals(matchData2.getResult().getHandicapResult())) {
            // 只要有一个延期或者让球无赔率,都直接插入特色数据
            JCBuyResultDataUtil.insertHandicapBuyResultData(dateStr, num, "X", "", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        String result_handicap1 = matchData1.getResult().getHandicapResult();
        String result_handicap2 = matchData2.getResult().getHandicapResult();
        String typeString = JCBaseUtil.getTypeString(matchData1, matchData2, null, null);
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, null, null);
        String result_handicap = result_handicap1 + "_" + result_handicap2;
        double odds_handicap = Double.parseDouble(matchData1.getResult().getHandicapOdds()) * Double.parseDouble(matchData2.getResult().getHandicapOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_handicap));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertHandicapBuyResultData(dateStr, num, result_handicap, bd.toString(), team, typeString, lastMatch, feMatch, fsMatch, 2);
    }

    public static void setHandicapResultData3(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum();
        boolean isExist = JCBuyResultDataUtil.searchHandicapBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("延期".equals(matchData1.getResult().getHandicapResult()) || "延期".equals(matchData2.getResult().getHandicapResult()) || "延期".equals(matchData3.getResult().getHandicapResult())) {
            // 只要有一个延期或者让球无赔率,都直接插入特色数据
            JCBuyResultDataUtil.insertHandicapBuyResultData(dateStr, num, "X", "", "", "", 0, 0, 0, 3);
            return;
        }
        String result_handicap1 = matchData1.getResult().getHandicapResult();
        String result_handicap2 = matchData2.getResult().getHandicapResult();
        String result_handicap3 = matchData3.getResult().getHandicapResult();
        String typeString = JCBaseUtil.getTypeString(matchData1, matchData2, matchData3, null);

        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, null);
        String result_handicap = result_handicap1 + "_" + result_handicap2 + "_" + result_handicap3;
        double odds_handicap = Double.parseDouble(matchData1.getResult().getHandicapOdds()) * Double.parseDouble(matchData2.getResult().getHandicapOdds()) * Double.parseDouble(matchData3.getResult().getHandicapOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_handicap));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertHandicapBuyResultData(dateStr, num, result_handicap, bd.toString(), team, typeString, 0, 0, 0, 3);
    }

    public static void setHandicapResultData4(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3, MatchData matchData4) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum() + "_" + matchData4.getNum();
        boolean isExist = JCBuyResultDataUtil.searchHandicapBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("延期".equals(matchData1.getResult().getHandicapResult()) || "延期".equals(matchData2.getResult().getHandicapResult()) || "延期".equals(matchData3.getResult().getHandicapResult()) || "延期".equals(matchData4.getResult().getHandicapResult())) {
            // 只要有一个延期或者让球无赔率,都直接插入特色数据
            JCBuyResultDataUtil.insertHandicapBuyResultData(dateStr, num, "X", "", "", "", 0, 0, 0, 4);
            return;
        }
        String result_handicap1 = matchData1.getResult().getHandicapResult();
        String result_handicap2 = matchData2.getResult().getHandicapResult();
        String result_handicap3 = matchData3.getResult().getHandicapResult();
        String result_handicap4 = matchData4.getResult().getHandicapResult();
        String typeString = JCBaseUtil.getTypeString(matchData1, matchData2, matchData3, matchData4);

        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, matchData4);
        String result_handicap = result_handicap1 + "_" + result_handicap2 + "_" + result_handicap3 + "_" + result_handicap4;
        double odds_handicap = Double.parseDouble(matchData1.getResult().getHandicapOdds()) * Double.parseDouble(matchData2.getResult().getHandicapOdds()) * Double.parseDouble(matchData3.getResult().getHandicapOdds()) * Double.parseDouble(matchData4.getResult().getHandicapOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_handicap));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertHandicapBuyResultData(dateStr, num, result_handicap, bd.toString(), team, typeString, 0, 0, 0, 4);
    }

    private static Map<String, Object> getHandicapResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        int[] values = {0, 1, 3};

        for (int first : values) {
            for (int second : values) {
                String key = first + "_" + second;
                map.put(key, 0);
                map.put(key+"_C", 0);
                map.put(key+"_T", "");
            }
        }
        return  map;
    }

    private static Map<String, Object> getHandicap3ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        int[] values = {0, 1, 3};

        for (int first : values) {
            for (int second : values) {
                for (int third : values) {
                    String key = first + "_" + second + "_" + third;
                    map.put(key, 0);
                    map.put(key+"_C", 0);
                    map.put(key+"_T", "");
                }
            }
        }
        return  map;
    }

    private static Map<String, Object> getHandicap4ResultMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("days", 0);  // 可用天数
        int[] values = {0, 1, 3};

        for (int first : values) {
            for (int second : values) {
                for (int third : values) {
                    for (int fourth : values) {
                        String key = first + "_" + second + "_" + third + "_" + fourth;
                        map.put(key, 0);
                        map.put(key+"_C", 0);
                        map.put(key+"_T", "");
                    }
                }
            }
        }
        return  map;
    }

    private static void setHandicapStringResult(int index, double[] result, int cash) {
        result[index] += cash;
    }

    private static void queryStrongHandicapResultByDate(String dateStr, BuyTableDAO buyTableDAO, double[] cash_fs, double[] cash_fe, double[] cash_end, int type) {
        // 正正
        JCBuyResultModel model1 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_3", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model2 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_0", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model3 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_0", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model4 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_3", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash1 = getHandicapResultCash(model1, model2, model3, model4);
        if (type == 0) {
            setHandicapStringResult(0, cash_fs, (int)(cash1 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(0, cash_fe, (int)(cash1 + 0.5));
        } else {
            setHandicapStringResult(0, cash_end, (int)(cash1 + 0.5));
        }

         // 负负
        JCBuyResultModel model11 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_0", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model22 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_3", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model33 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_3", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model44 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_0", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash2 = getHandicapResultCash(model11, model22, model33, model44);
        if (type == 0) {
            setHandicapStringResult(1, cash_fs, (int)(cash2 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(1, cash_fe, (int)(cash2 + 0.5));
        } else {
            setHandicapStringResult(1, cash_end, (int)(cash2 + 0.5));
        }

        // 负正
        JCBuyResultModel model111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_3", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_0", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_0", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_3", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash3 = getHandicapResultCash(model111, model222, model333, model444);
        if (type == 0) {
            setHandicapStringResult(2, cash_fs, (int)(cash3 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(2, cash_fe, (int)(cash3 + 0.5));
        } else {
            setHandicapStringResult(2, cash_end, (int)(cash3 + 0.5));
        }

        // 正负
        JCBuyResultModel model1111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_0", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model2222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_3", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model3333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_3", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model4444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_0", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash4 = getHandicapResultCash(model1111, model2222, model3333, model4444);
        if (type == 0) {
            setHandicapStringResult(3, cash_fs, (int)(cash4 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(3, cash_fe, (int)(cash4 + 0.5));
        } else {
            setHandicapStringResult(3, cash_end, (int)(cash4 + 0.5));
        }

        // 正平
        JCBuyResultModel model11111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_1", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model22222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_1", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model33333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_1", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model44444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_1", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash5 = getHandicapResultCash(model11111, model22222, model33333, model44444);
        if (type == 0) {
            setHandicapStringResult(4, cash_fs, (int)(cash5 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(4, cash_fe, (int)(cash5 + 0.5));
        } else {
            setHandicapStringResult(4, cash_end, (int)(cash5 + 0.5));
        }

        // 平平
        JCBuyResultModel model111111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_1", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model222222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_1", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model333333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_1", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model444444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_1", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash6 = getHandicapResultCash(model111111, model222222, model333333, model444444);
        if (type == 0) {
            setHandicapStringResult(5, cash_fs, (int)(cash6 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(5, cash_fe, (int)(cash6 + 0.5));
        } else {
            setHandicapStringResult(5, cash_end, (int)(cash6 + 0.5));
        }

        // 负平
        JCBuyResultModel model1111111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_1", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model2222222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_1", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model3333333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "0_1", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model4444444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "3_1", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash7 = getHandicapResultCash(model1111111, model2222222, model3333333, model4444444);
        if (type == 0) {
            setHandicapStringResult(6, cash_fs, (int)(cash7 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(6, cash_fe, (int)(cash7 + 0.5));
        } else {
            setHandicapStringResult(6, cash_end, (int)(cash7 + 0.5));
        }

        // 平正
        JCBuyResultModel model11111111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_3", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model22222222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_0", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model33333333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_0", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model44444444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_3", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash8 = getHandicapResultCash(model11111111, model22222222, model33333333, model44444444);
        if (type == 0) {
            setHandicapStringResult(7, cash_fs, (int)(cash8 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(7, cash_fe, (int)(cash8 + 0.5));
        } else {
            setHandicapStringResult(7, cash_end, (int)(cash8 + 0.5));
        }

        // 平负
        JCBuyResultModel model111111111 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_0", "11", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model222222222 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_3", "00", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model333333333 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_3", "10", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        JCBuyResultModel model444444444 = buyTableDAO.searchHandicapBuyResultByResult(dateStr, 2, "1_0", "01", type == 0 ? 1 : 0, type == 1 ? 1 : 0, type == 2 ? 1 : 0);
        double cash9 = getHandicapResultCash(model111111111, model222222222, model333333333, model444444444);
        if (type == 0) {
            setHandicapStringResult(8, cash_fs, (int)(cash9 + 0.5));
        } else if (type == 1) {
            setHandicapStringResult(8, cash_fe, (int)(cash9 + 0.5));
        } else {
            setHandicapStringResult(8, cash_end, (int)(cash9 + 0.5));
        }
    }

    private static double getHandicapResultCash(JCBuyResultModel model1, JCBuyResultModel model2, JCBuyResultModel model3, JCBuyResultModel model4) {
        List<JCBuyResultModel> list = new ArrayList<>();
        list.add(model1);
        list.add(model2);
        list.add(model3);
        list.add(model4);
        return getCash(list);
    }

    private static void queryBaseHandicapResultByDate(String dateStr, BuyTableDAO buyTableDAO, int matchNum, Map<String, Object> map, int fsMatch, int feMatch, int endMatch) {
        JCBuyResultModel handicapResultModel = buyTableDAO.searchHandicapBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, matchNum, fsMatch, feMatch, endMatch);
        if (handicapResultModel != null && !handicapResultModel.getResult().contains("X")) {
            int cash = (int)(JCBaseUtil.calculateCash(handicapResultModel.getOdds()) + 0.5);
            String result = handicapResultModel.getResult();
            if (map.containsKey(result)) {
                String cashAndCnt = (String)map.get(result);
                if (cashAndCnt.contains("_")) {
                    String[] cashAndCntArr = cashAndCnt.split("_");
                    int cashOld = Integer.parseInt(cashAndCntArr[0]);
                    int cntOld = Integer.parseInt(cashAndCntArr[1]);
                    map.put(result, cashOld + cash + "_" + (cntOld + 1));
                }
            } else {
                map.put(result, cash + "_1");
            }
        }
    }

    private static double getCash(List<JCBuyResultModel> list) {
        for (JCBuyResultModel resultModel : list) {
            if (resultModel != null) {
                return JCBaseUtil.calculateCash(resultModel.getOdds());
            }
        }
        return -100;
    }
}
