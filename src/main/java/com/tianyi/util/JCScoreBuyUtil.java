package com.tianyi.util;

import com.tianyi.dao.BuyTableDAO;
import com.tianyi.model.JCBuyResultModel;
import com.tianyi.model.MatchData;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class JCScoreBuyUtil {
    public static void queryBuyResultByMonthData(int year) {
        final BuyTableDAO buyTableDAO = new BuyTableDAO();

        Map<String, Integer> fsMap = new HashMap<>();
        Map<String, Integer> feMap = new HashMap<>();
        Map<String, Integer> endMap = new HashMap<>();
        Map<String, Integer> match3Map = new HashMap<>();
        Map<String, Integer> match4Map = new HashMap<>();

        Map<String, String> fsCntMap = new HashMap<>();
        Map<String, String> feCntMap = new HashMap<>();
        Map<String, String> endCntMap = new HashMap<>();
        Map<String, String> match3CntMap = new HashMap<>();
        Map<String, String> match4CntMap = new HashMap<>();

        for (int month = 1; month <= 12; month++) {
            // 获取该月的天数
            int daysInMonth = JCBaseUtil.getDaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++) {
                String dateStr = String.format("%d-%02d-%02d", year, month, day);
                boolean isExist = buyTableDAO.searchHalfFullBuyResultByDate(dateStr);
                if (!isExist) {
                    continue;
                }
                JCBuyResultModel scoreResultModel_FS = buyTableDAO.searchScoreBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 1, 0, 0);
                JCBuyResultModel scoreResultModel_FE = buyTableDAO.searchScoreBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 1, 0);
                JCBuyResultModel scoreResultModel_END = buyTableDAO.searchScoreBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 2, 0, 0, 1);
                JCBuyResultModel scoreResultModel4 = buyTableDAO.searchScoreBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 3, 0, 0, 0);
                JCBuyResultModel scoreResultModel5 = buyTableDAO.searchScoreBuyResultByDateAndMatchNumAndFsFeEnd(dateStr, 4, 0, 0, 0);

                JCBaseUtil.setScoreMap(fsMap, fsCntMap, scoreResultModel_FS, month, day);
                JCBaseUtil.setScoreMap(feMap, feCntMap, scoreResultModel_FE, month, day);
                JCBaseUtil.setScoreMap(endMap, endCntMap, scoreResultModel_END, month, day);
                JCBaseUtil.setScoreMap(match3Map, match3CntMap, scoreResultModel4, month, day);
                JCBaseUtil.setScoreMap(match4Map, match4CntMap, scoreResultModel5, month, day);
            }
        }

        Map<String, Integer> sortFsMap = new TreeMap<>(String::compareTo);
        sortFsMap.putAll(fsMap);
        Map<String, Integer> sortFeMap = new TreeMap<>(String::compareTo);
        sortFeMap.putAll(feMap);
        Map<String, Integer> sortEndMap = new TreeMap<>(String::compareTo);
        sortEndMap.putAll(endMap);
        Map<String, Integer> sort3Map = new TreeMap<>(String::compareTo);
        sort3Map.putAll(match3Map);
        Map<String, Integer> sort4Map = new TreeMap<>(String::compareTo);
        sort4Map.putAll(match4Map);

        System.out.println("------ FS -------");
        JCBaseUtil.printScoreMap(sortFsMap, fsCntMap);
        System.out.println("------ FE -------");
        JCBaseUtil.printScoreMap(sortFeMap, feCntMap);
        System.out.println("------ END -------");
        JCBaseUtil.printScoreMap(sortEndMap, endCntMap);
        System.out.println("------ M3 -------");
        JCBaseUtil.printScoreMap(sort3Map, match3CntMap);
        System.out.println("------ M4 -------");
        JCBaseUtil.printScoreMap(sort4Map, match4CntMap);
    }

    public static void setScoreResultData2(String dateStr, MatchData matchData1, MatchData matchData2, int lastMatch, int feMatch, int fsMatch) {
        String num = matchData1.getNum() + "_" + matchData2.getNum();
        boolean isExist = JCBuyResultDataUtil.searchScoreBuyResultDataByFsFeEnd(dateStr, num, fsMatch, feMatch, lastMatch);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, "X", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        if ("".equals(matchData1.getResult().getScoreResult()) || "".equals(matchData2.getResult().getScoreResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, "X", "", "", lastMatch, feMatch, fsMatch, 2);
            return;
        }
        String result_full1 = matchData1.getResult().getScoreResult();
        String result_full2 = matchData2.getResult().getScoreResult();

        String team = JCBaseUtil.getTeamString(matchData1, matchData2, null, null);
        String result_full = result_full1 + "_" + result_full2;
        double odds_full = Double.parseDouble(matchData1.getResult().getScoreOdds()) * Double.parseDouble(matchData2.getResult().getScoreOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, result_full, bd.toString(), team, lastMatch, feMatch, fsMatch, 2);
    }

    public static void setScoreResultData3(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum();
        boolean isExist = JCBuyResultDataUtil.searchScoreBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull()) || "-".equals(matchData3.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 3);
            return;
        }
        if ("".equals(matchData1.getResult().getScoreResult()) || "".equals(matchData2.getResult().getScoreResult()) || "".equals(matchData3.getResult().getScoreResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 3);
            return;
        }
        String result_full1 = matchData1.getResult().getScoreResult();
        String result_full2 = matchData2.getResult().getScoreResult();
        String result_full3 = matchData3.getResult().getScoreResult();
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, null);
        String result_full = result_full1 + "_" + result_full2 + "_" + result_full3;

        double odds_full = Double.parseDouble(matchData1.getResult().getScoreOdds()) * Double.parseDouble(matchData2.getResult().getScoreOdds()) * Double.parseDouble(matchData3.getResult().getScoreOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, result_full, bd.toString(), team, 0, 0, 0, 3);
    }

    public static void setScoreResultData4(String dateStr, MatchData matchData1, MatchData matchData2, MatchData matchData3, MatchData matchData4) {
        String num = matchData1.getNum() + "_" + matchData2.getNum() + "_" + matchData3.getNum() + "_" + matchData4.getNum();
        boolean isExist = JCBuyResultDataUtil.searchScoreBuyResultData(dateStr, num);
        if (isExist) {
            System.out.println("数据已存在,跳过当前操作");
            return;
        }
        if ("-".equals(matchData1.getResult().getFull()) || "-".equals(matchData2.getResult().getFull()) || "-".equals(matchData3.getResult().getFull()) || "-".equals(matchData4.getResult().getFull())) {
            // 只要有一个延期,都直接插入特色数据
            JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 4);
            return;
        }
        if ("".equals(matchData1.getResult().getScoreResult()) || "".equals(matchData2.getResult().getScoreResult()) || "".equals(matchData3.getResult().getScoreResult()) || "".equals(matchData4.getResult().getScoreResult())) {
            // 未开放也不处理
            JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, "X", "", "", 0, 0, 0, 4);
            return;
        }
        String result_full1 = matchData1.getResult().getScoreResult();
        String result_full2 = matchData2.getResult().getScoreResult();
        String result_full3 = matchData3.getResult().getScoreResult();
        String result_full4 = matchData4.getResult().getScoreResult();
        String team = JCBaseUtil.getTeamString(matchData1, matchData2, matchData3, matchData4);
        String result_full = result_full1 + "_" + result_full2 + "_" + result_full3 + "_" + result_full4;

        double odds_full = Double.parseDouble(matchData1.getResult().getScoreOdds()) * Double.parseDouble(matchData2.getResult().getScoreOdds()) * Double.parseDouble(matchData3.getResult().getScoreOdds()) * Double.parseDouble(matchData4.getResult().getScoreOdds());
        BigDecimal bd = new BigDecimal(Double.toString(odds_full));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        JCBuyResultDataUtil.insertScoreBuyResultData(dateStr, num, result_full, bd.toString(), team, 0, 0, 0, 4);
    }
}
