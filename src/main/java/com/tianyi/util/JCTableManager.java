package com.tianyi.util;

import com.tianyi.sql.JCBuyResultTableSQLStatements;

import java.sql.*;
import java.util.Scanner;

/**
 * 表管理工具类
 * 用于创建、删除、清空、重置表等危险操作
 */
public class JCTableManager {
    
    private static final Scanner scanner = new Scanner(System.in);
    
    /**
     * 删除表（危险操作）
     */
    public static void dropJCTable() {
        System.out.println("⚠️  警告：您即将删除整个竞彩表！");
        System.out.println("这将删除表结构和所有数据，且无法恢复！");
        System.out.print("请输入 'DELETE TABLE' 来确认操作：");
        
        String confirmation = scanner.nextLine().trim();
        if (!"DELETE TABLE".equals(confirmation)) {
            System.out.println("操作已取消");
            return;
        }
        
        Connection connection = null;
        Statement statement = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();
            
            // 执行删除表操作
//            statement.executeUpdate(JCTableSQLStatements.getDropJCTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getDropHalfFullTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getDropHandicapTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getDropGoalTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getDropScoreTableSQL());
            
            System.out.println("✅ 删除表成功");
            return;
            
        } catch (SQLException e) {
            System.err.println("❌ 删除表失败: " + e.getMessage());
            return;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

    /**
     * 创建表
     */
    public static void creatJCTable() {

        Connection connection = null;
        Statement statement = null;

        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();

            // 执行创建表操作
//            statement.executeUpdate(JCTableSQLStatements.getDropJCTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getCreateHalfFullTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getCreateHandicapTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getCreateGoalTableSQL());
            statement.executeUpdate(JCBuyResultTableSQLStatements.getCreateScoreTableSQL());

            System.out.println("✅ 创建表成功");
            return;

        } catch (SQLException e) {
            System.err.println("❌ 创建表失败: " + e.getMessage());
            return;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

}
