package com.tianyi.util;

import com.tianyi.sql.JCEndResultSQLStatements;
import com.tianyi.sql.JCFEResultSQLStatements;
import com.tianyi.sql.JCFSResultSQLStatements;
import com.tianyi.sql.JCTableSQLStatements;

import java.sql.*;
import java.util.Scanner;

/**
 * 竞彩表管理工具类
 * 用于删除、清空、重置竞彩表等危险操作
 */
public class JCTableManager {
    
    private static final Scanner scanner = new Scanner(System.in);
    
    /**
     * 删除竞彩表（危险操作）
     */
    public static void dropJCTable() {
        System.out.println("⚠️  警告：您即将删除整个竞彩表！");
        System.out.println("这将删除表结构和所有数据，且无法恢复！");
        System.out.print("请输入 'DELETE TABLE' 来确认操作：");
        
        String confirmation = scanner.nextLine().trim();
        if (!"DELETE TABLE".equals(confirmation)) {
            System.out.println("操作已取消");
            return;
        }
        
        Connection connection = null;
        Statement statement = null;
        
        try {
            connection = DatabaseUtil.getConnection();
            statement = connection.createStatement();
            
            // 执行删除表操作
//            statement.executeUpdate(JCTableSQLStatements.getDropJCTableSQL());
            statement.executeUpdate(JCEndResultSQLStatements.getDropEndTableSQL());
            statement.executeUpdate(JCFEResultSQLStatements.getDropFETableSQL());
            statement.executeUpdate(JCFSResultSQLStatements.getDropFSTableSQL());
            
            System.out.println("✅ 竞彩表已成功删除");
            return;
            
        } catch (SQLException e) {
            System.err.println("❌ 删除竞彩表失败: " + e.getMessage());
            return;
        } finally {
            DatabaseUtil.closeResources(connection, statement, null);
        }
    }

}
