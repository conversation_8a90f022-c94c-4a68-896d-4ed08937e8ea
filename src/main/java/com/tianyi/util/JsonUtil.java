package com.tianyi.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tianyi.dao.BuyTableType;
import com.tianyi.model.MatchData;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON处理工具类
 * 用于读取和解析JSON文件
 */
public class JsonUtil {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 从JSON文件读取比赛数据列表
     */
    public static List<MatchData> readMatchDataFromFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                System.err.println("JSON文件不存在: " + filePath);
                return null;
            }

            // 配置ObjectMapper以处理未知属性和空值
            objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

            // 先读取为JsonNode来检查数据结构
            com.fasterxml.jackson.databind.JsonNode rootNode = objectMapper.readTree(file);

            if (!rootNode.isArray()) {
                System.err.println("JSON文件根节点不是数组");
                return null;
            }

            List<MatchData> matchList = new ArrayList<>();

            for (int i = 0; i < rootNode.size(); i++) {
                com.fasterxml.jackson.databind.JsonNode node = rootNode.get(i);

                try {
                    if (node.isObject()) {
                        MatchData match = objectMapper.treeToValue(node, MatchData.class);
                        if (match != null) {
                            matchList.add(match);
                        }
                    } else {
                        System.err.println("第 " + i + " 个元素不是对象，跳过: " + node.toString());
                    }
                } catch (Exception e) {
                    System.err.println("解析第 " + i + " 个元素失败: " + e.getMessage());
                    System.err.println("元素内容: " + node.toString());
                }
            }

            System.out.println("成功解析 " + matchList.size() + " 个比赛数据");
            return matchList;

        } catch (IOException e) {
            System.err.println("失败文件地址: " + filePath);
            System.err.println("读取JSON文件失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 打印比赛数据列表的摘要信息
     */
    public static void printMatchSummary(List<MatchData> matchList) {
        if (matchList == null || matchList.isEmpty()) {
            System.out.println("没有比赛数据");
            return;
        }
        
        System.out.println("=== 比赛数据摘要 ===");
        System.out.println("总共 " + matchList.size() + " 场比赛");
        System.out.println();
        
        System.out.printf("%-10s %-15s %-10s %-15s %-15s %-10s %-10s%n", 
                         "场次", "时间", "联赛", "主队", "客队", "半场", "全场");
        System.out.println("=".repeat(100));
        
        for (MatchData match : matchList) {
            MatchData.MatchResult result = match.getResult();
            System.out.printf("%-10s %-15s %-10s %-15s %-15s %-10s %-10s%n",
                match.getNum(),
                match.getMatchTime(),
                match.getLeague(),
                match.getHome(),
                match.getAway(),
                result != null ? result.getHalf() : "N/A",
                result != null ? result.getFull() : "N/A"
            );
        }
        System.out.println();
    }

    /**
     * 插入详细比赛信息到数据库
     */
    public static void insertDetailedMatches(List<MatchData> matchList, String timeStr) {
        if (matchList == null || matchList.isEmpty()) {
            System.out.println("没有比赛数据");
            return;
        }

        // 插入竞彩数据
//        for (MatchData matchData : matchList) {
//            matchData.insertDetailedInfo(timeStr);
//        }
    }
    
    /**
     * 按联赛统计比赛数量
     */
    public static void printLeagueStatistics(List<MatchData> matchList) {
        if (matchList == null || matchList.isEmpty()) {
            System.out.println("没有比赛数据");
            return;
        }
        
        System.out.println("=== 联赛统计 ===");
        
        // 使用Map统计各联赛的比赛数量
        java.util.Map<String, Integer> leagueCount = new java.util.HashMap<>();
        
        for (MatchData match : matchList) {
            String league = match.getLeague();
            leagueCount.put(league, leagueCount.getOrDefault(league, 0) + 1);
        }
        
        // 按比赛数量排序并打印
        leagueCount.entrySet().stream()
            .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
            .forEach(entry -> 
                System.out.println(entry.getKey() + ": " + entry.getValue() + " 场比赛")
            );
        
        System.out.println();
    }
    
    /**
     * 根据联赛筛选比赛
     */
    public static List<MatchData> filterByLeague(List<MatchData> matchList, String league) {
        if (matchList == null) {
            return null;
        }
        
        return matchList.stream()
            .filter(match -> league.equals(match.getLeague()))
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 验证JSON文件格式
     */
    public static boolean validateJsonFile(String filePath) {
        try {
            List<MatchData> matches = readMatchDataFromFile(filePath);
            if (matches == null) {
                return false;
            }
            
            System.out.println("JSON文件验证成功！");
            System.out.println("文件路径: " + filePath);
            System.out.println("包含 " + matches.size() + " 场比赛数据");
            
            // 检查数据完整性
            long validMatches = matches.stream()
                .filter(match -> match.getLeague() != null && !match.getLeague().isEmpty())
                .filter(match -> match.getHome() != null && !match.getHome().isEmpty())
                .filter(match -> match.getAway() != null && !match.getAway().isEmpty())
                .count();
            
            System.out.println("有效比赛数据: " + validMatches + " 场");
            
            return true;
            
        } catch (Exception e) {
            System.err.println("JSON文件验证失败: " + e.getMessage());
            return false;
        }
    }
}
