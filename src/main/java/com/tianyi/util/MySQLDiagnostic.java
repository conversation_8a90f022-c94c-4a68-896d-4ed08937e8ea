package com.tianyi.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Scanner;

/**
 * MySQL连接诊断工具
 * 帮助诊断和解决MySQL连接问题
 */
public class MySQLDiagnostic {
    
    public static void main(String[] args) {
        System.out.println("=== MySQL连接诊断工具 ===");
        System.out.println();
        
        Scanner scanner = new Scanner(System.in);
        
        // 获取用户输入的连接信息
        System.out.print("请输入MySQL主机地址 (默认: localhost): ");
        String host = scanner.nextLine().trim();
        if (host.isEmpty()) host = "localhost";
        
        System.out.print("请输入MySQL端口 (默认: 3306): ");
        String portStr = scanner.nextLine().trim();
        String port = portStr.isEmpty() ? "3306" : portStr;
        
        System.out.print("请输入MySQL用户名 (默认: root): ");
        String username = scanner.nextLine().trim();
        if (username.isEmpty()) username = "root";
        
        System.out.print("请输入MySQL密码: ");
        String password = scanner.nextLine();
        
        // 测试连接
        testConnection(host, port, username, password);
        
        scanner.close();
    }
    
    /**
     * 测试MySQL连接
     */
    public static void testConnection(String host, String port, String username, String password) {
        System.out.println();
        System.out.println("=== 开始连接测试 ===");
        System.out.println("主机: " + host);
        System.out.println("端口: " + port);
        System.out.println("用户名: " + username);
        System.out.println("密码: " + (password.isEmpty() ? "空" : "已设置"));
        System.out.println();
        
        String jdbcUrl = "jdbc:mysql://" + host + ":" + port + 
                        "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
        
        Connection connection = null;
        
        try {
            // 1. 检查JDBC驱动
            System.out.println("1. 检查MySQL JDBC驱动...");
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("   ✓ MySQL JDBC驱动加载成功");
            
            // 2. 尝试连接
            System.out.println("2. 尝试连接MySQL服务器...");
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            System.out.println("   ✓ 连接成功！");
            
            // 3. 测试基本操作
            System.out.println("3. 测试数据库操作权限...");
            var statement = connection.createStatement();
            var resultSet = statement.executeQuery("SELECT VERSION() as version");
            if (resultSet.next()) {
                System.out.println("   ✓ MySQL版本: " + resultSet.getString("version"));
            }
            
            // 4. 检查创建数据库权限
            System.out.println("4. 检查创建数据库权限...");
            try {
                statement.executeUpdate("CREATE DATABASE IF NOT EXISTS test_tianyi_temp");
                statement.executeUpdate("DROP DATABASE IF EXISTS test_tianyi_temp");
                System.out.println("   ✓ 具有创建数据库权限");
            } catch (SQLException e) {
                System.out.println("   ✗ 没有创建数据库权限: " + e.getMessage());
            }
            
            resultSet.close();
            statement.close();
            
            System.out.println();
            System.out.println("=== 诊断完成 ===");
            System.out.println("连接配置正确，可以使用此配置！");
            
        } catch (ClassNotFoundException e) {
            System.out.println("   ✗ MySQL JDBC驱动未找到");
            System.out.println("   请确保项目中包含mysql-connector-java依赖");
        } catch (SQLException e) {
            System.out.println("   ✗ 连接失败: " + e.getMessage());
            System.out.println();
            
            // 提供具体的解决建议
            if (e.getMessage().contains("Access denied")) {
                System.out.println("=== 解决建议 ===");
                System.out.println("访问被拒绝，可能的原因：");
                System.out.println("1. 用户名或密码错误");
                System.out.println("2. 用户没有从localhost连接的权限");
                System.out.println("3. MySQL用户账户被锁定");
                System.out.println();
                System.out.println("解决方法：");
                System.out.println("1. 重置MySQL root密码：");
                System.out.println("   sudo mysql -u root -p");
                System.out.println("   ALTER USER 'root'@'localhost' IDENTIFIED BY 'your_password';");
                System.out.println("   FLUSH PRIVILEGES;");
                System.out.println();
                System.out.println("2. 或创建新用户：");
                System.out.println("   CREATE USER 'tianyi'@'localhost' IDENTIFIED BY 'password';");
                System.out.println("   GRANT ALL PRIVILEGES ON *.* TO 'tianyi'@'localhost';");
                System.out.println("   FLUSH PRIVILEGES;");
            } else if (e.getMessage().contains("Connection refused")) {
                System.out.println("=== 解决建议 ===");
                System.out.println("连接被拒绝，可能的原因：");
                System.out.println("1. MySQL服务未启动");
                System.out.println("2. 端口号错误");
                System.out.println("3. 防火墙阻止连接");
                System.out.println();
                System.out.println("解决方法：");
                System.out.println("1. 启动MySQL服务：");
                System.out.println("   macOS: brew services start mysql");
                System.out.println("   Linux: sudo systemctl start mysql");
                System.out.println("   Windows: net start mysql");
                System.out.println();
                System.out.println("2. 检查MySQL是否在运行：");
                System.out.println("   ps aux | grep mysql");
            }
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    System.out.println("关闭连接失败: " + e.getMessage());
                }
            }
        }
    }
}
