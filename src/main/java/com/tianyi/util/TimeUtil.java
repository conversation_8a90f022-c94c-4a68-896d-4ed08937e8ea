package com.tianyi.util;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 时间处理工具类
 * 用于处理timeStr和JSON中比赛时间的组合转换
 */
public class TimeUtil {
    
    // 时间格式化器
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter SIMPLE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter JC_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    // 星期映射
    private static final Map<String, DayOfWeek> WEEK_DAY_MAP = new HashMap<>();
    static {
        WEEK_DAY_MAP.put("周一", DayOfWeek.MONDAY);
        WEEK_DAY_MAP.put("周二", DayOfWeek.TUESDAY);
        WEEK_DAY_MAP.put("周三", DayOfWeek.WEDNESDAY);
        WEEK_DAY_MAP.put("周四", DayOfWeek.THURSDAY);
        WEEK_DAY_MAP.put("周五", DayOfWeek.FRIDAY);
        WEEK_DAY_MAP.put("周六", DayOfWeek.SATURDAY);
        WEEK_DAY_MAP.put("周日", DayOfWeek.SUNDAY);
        WEEK_DAY_MAP.put("周天", DayOfWeek.SUNDAY);
    }
    
    /**
     * 将timeStr和比赛时间组合转换为完整的时间格式
     * 
     * @param timeStr 时间字符串，如 "2025年1月"
     * @param matchTime JSON中的比赛时间，如 "01-01 12:00"
     * @return 格式化后的完整时间字符串
     */
    public static String combineDateTime(String timeStr, String matchTime) {
        try {
            // 1. 解析timeStr获取年份和月份
            int year = extractYear(timeStr);
            int month = extractMonth(timeStr);
            
            // 2. 解析matchTime获取日期和时间
            String[] dateParts = parseMatchTime(matchTime);
            int day = Integer.parseInt(dateParts[0]);
            int hour = Integer.parseInt(dateParts[1]);
            int minute = Integer.parseInt(dateParts[2]);
            
            // 3. 组合成完整的LocalDateTime
            LocalDateTime dateTime = LocalDateTime.of(year, month, day, hour, minute);
            
            // 4. 格式化输出
            return dateTime.format(OUTPUT_FORMATTER);
            
        } catch (Exception e) {
            System.err.println("时间转换失败: " + e.getMessage());
            return timeStr + " " + matchTime; // 返回原始组合
        }
    }
    
    /**
     * 获取简化的时间格式（不含秒）
     */
    public static String combineSimpleDateTime(String timeStr, String matchTime) {
        try {
            int year = extractYear(timeStr);
            int month = extractMonth(timeStr);
            
            String[] dateParts = parseMatchTime(matchTime);
            int day = Integer.parseInt(dateParts[0]);
            int hour = Integer.parseInt(dateParts[1]);
            int minute = Integer.parseInt(dateParts[2]);
            
            LocalDateTime dateTime = LocalDateTime.of(year, month, day, hour, minute);
            return dateTime.format(SIMPLE_FORMATTER);
            
        } catch (Exception e) {
            System.err.println("简化时间转换失败: " + e.getMessage());
            return timeStr + " " + matchTime;
        }
    }
    
    /**
     * 从timeStr中提取年份
     * 支持格式：2025年1月、2025年12月等
     */
    private static int extractYear(String timeStr) {
        Pattern yearPattern = Pattern.compile("(\\d{4})年");
        Matcher matcher = yearPattern.matcher(timeStr);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        throw new IllegalArgumentException("无法从timeStr中提取年份: " + timeStr);
    }
    
    /**
     * 从timeStr中提取月份
     * 支持格式：2025年1月、2025年12月等
     */
    private static int extractMonth(String timeStr) {
        Pattern monthPattern = Pattern.compile("(\\d{1,2})月");
        Matcher matcher = monthPattern.matcher(timeStr);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        throw new IllegalArgumentException("无法从timeStr中提取月份: " + timeStr);
    }
    
    /**
     * 解析JSON中的比赛时间
     * 支持格式：01-01 12:00、12-31 23:59等
     */
    private static String[] parseMatchTime(String matchTime) {
        Pattern timePattern = Pattern.compile("(\\d{2})-(\\d{2})\\s+(\\d{2}):(\\d{2})");
        Matcher matcher = timePattern.matcher(matchTime);
        if (matcher.find()) {
            String month = matcher.group(1);
            String day = matcher.group(2);
            String hour = matcher.group(3);
            String minute = matcher.group(4);
            return new String[]{day, hour, minute};
        }
        throw new IllegalArgumentException("无法解析比赛时间格式: " + matchTime);
    }
    
    /**
     * 批量转换比赛时间（包含竞彩时间）
     */
    public static void printFormattedMatchTimes(String timeStr, java.util.List<com.tianyi.model.MatchData> matchList) {
        System.out.println("=== 格式化后的比赛时间 ===");
        System.out.printf("%-10s %-20s %-20s %-20s %-15s %-15s%n",
                         "场次", "原始时间", "格式化时间", "竞彩时间", "主队", "客队");
        System.out.println("=".repeat(120));

        for (com.tianyi.model.MatchData match : matchList) {
            String originalTime = match.getMatchTime();
            String formattedTime = combineDateTime(timeStr, originalTime);
            String jcTime = getJCTimeFromWeekday(timeStr, originalTime);

            System.out.printf("%-10s %-20s %-20s %-20s %-15s %-15s%n",
                match.getNum(),
                originalTime,
                formattedTime,
                jcTime,
                match.getHome(),
                match.getAway()
            );
        }
        System.out.println();
    }
    
    /**
     * 根据场次星期转换成竞彩时间（2025-01-01格式）
     *
     * @param timeStr 时间字符串，如 "2025年1月"
     * @param matchTime JSON中的比赛时间，如 "01-01 12:00"
     * @return 竞彩时间字符串，格式：2025-01-01 12:00
     */
    public static String getJCTimeFromWeekday(String timeStr, String matchTime) {
        try {
            // 1. 从timeStr中提取年月信息
            int year = extractYear(timeStr);

            return year + "-" + matchTime;

        } catch (Exception e) {
            System.err.println("竞彩时间转换失败: " + e.getMessage());
            return timeStr + " " + matchTime; // 返回原始组合
        }
    }

    /**
     * 获取竞彩日期（只返回日期部分，如2025-01-01）
     */
    /**
     * 获取竞彩日期（根据场次星期调整日期）
     *
     * @param timeStr   时间字符串，如 "2025年1月"
     * @param matchTime JSON中的比赛时间，如 "01-08 03:00"
     * @param num       场次编号，如 "周三001"
     * @return 竞彩日期字符串，格式：yyyy-MM-dd
     */
    public static String getJCDateFromWeekday(String timeStr, String matchTime, String num) {
        try {
            // 1. 提取年份和月份
            int year = extractYear(timeStr);
            int month = extractMonth(timeStr);

            // 2. 解析matchTime的日期部分
            String[] parts = parseMatchTime(matchTime);
            int day = Integer.parseInt(parts[0]);
            int hour = Integer.parseInt(parts[1]);
            int minute = Integer.parseInt(parts[2]);

            // 3. 构建完整时间
            LocalDateTime matchDateTime = LocalDateTime.of(year, month, day, hour, minute);

            // 4. 从num中提取星期几
            String weekdayStr = extractWeekdayFromNum(num);
            DayOfWeek expectedDay = WEEK_DAY_MAP.get(weekdayStr);

            if (expectedDay == null) {
                throw new IllegalArgumentException("无法识别的星期: " + weekdayStr);
            }

            // 5. 判断matchDateTime的星期几是否匹配
            DayOfWeek actualDay = matchDateTime.getDayOfWeek();
            if (actualDay != expectedDay) {
                // 不匹配
                int diff = (actualDay.getValue() - expectedDay.getValue() + 7) % 7;
                matchDateTime = matchDateTime.minusDays(diff);
            }

            // 6. 返回格式化后的日期
            return matchDateTime.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);

        } catch (DateTimeParseException | IllegalArgumentException e) {
            System.err.println("竞彩日期转换失败: " + e.getMessage());
            return timeStr; // 出错时返回原始时间字符串
        }
    }


    /**
     * 从场次编号中提取星期信息
     * 如：周三001 -> 周三，周五002 -> 周五
     */
    public static String extractWeekdayFromNum(String num) {
        Pattern weekdayPattern = Pattern.compile("(周[一二三四五六日天])\\d+");
        Matcher matcher = weekdayPattern.matcher(num);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new IllegalArgumentException("无法从场次编号中提取星期信息: " + num);
    }

    /**
     * 从比赛时间中提取小时和分钟
     * 如：01-01 12:00 -> [12, 00]
     */
    private static String[] parseMatchTimeForHourMinute(String matchTime) {
        Pattern timePattern = Pattern.compile("\\d{2}-\\d{2}\\s+(\\d{2}):(\\d{2})");
        Matcher matcher = timePattern.matcher(matchTime);
        if (matcher.find()) {
            String hour = matcher.group(1);
            String minute = matcher.group(2);
            return new String[]{hour, minute};
        }
        throw new IllegalArgumentException("无法解析比赛时间格式: " + matchTime);
    }

    /**
     * 获取竞彩时间（用于数据库存储）
     */
    public static String getJCTime(String timeStr, String matchTime) {
        return combineSimpleDateTime(timeStr, matchTime);
    }
    
    /**
     * 验证时间格式是否正确
     */
    public static boolean validateTimeFormat(String timeStr, String matchTime) {
        try {
            combineDateTime(timeStr, matchTime);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取时间转换的详细信息
     */
    public static void printTimeConversionDetails(String timeStr, String matchTime) {
        System.out.println("=== 时间转换详情 ===");
        System.out.println("输入timeStr: " + timeStr);
        System.out.println("输入matchTime: " + matchTime);
        
        try {
            int year = extractYear(timeStr);
            int month = extractMonth(timeStr);
            String[] parts = parseMatchTime(matchTime);
            
            System.out.println("解析结果:");
            System.out.println("  年份: " + year);
            System.out.println("  月份: " + month);
            System.out.println("  日期: " + parts[0]);
            System.out.println("  小时: " + parts[1]);
            System.out.println("  分钟: " + parts[2]);
            
            String fullTime = combineDateTime(timeStr, matchTime);
            String simpleTime = combineSimpleDateTime(timeStr, matchTime);
            
            System.out.println("转换结果:");
            System.out.println("  完整时间: " + fullTime);
            System.out.println("  简化时间: " + simpleTime);
            
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println("==================");
    }
}
